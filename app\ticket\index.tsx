import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  Image,
  Modal as RNModal,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  MD3LightTheme,
  Provider as PaperProvider,
  Surface,
  Text,
  Title,
} from 'react-native-paper';
import { confirmPayment } from '../../api/orderService';

const { width, height } = Dimensions.get('window');

// Custom theme for React Native Paper
const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#2D5BFF',
    secondary: '#FF6B6B',
    tertiary: '#4ECDC4',
    surface: '#FFFFFF',
    background: '#F8FAFF',
    surfaceVariant: 'rgba(255, 255, 255, 0.8)',
  },
};

// Interface cho dữ liệu vé
interface TicketData {
  ticketId: string;
  busCompany: string;
  route: string;
  date: string;
  time: string;
  duration: string;
  arrivalTime: string;
  price: number;
  quantity: number;
  totalPrice: number;
  busType: string;
  pickupLocation: {
    id?: string;
    name: string;
    address: string;
    time: string;
  };
  dropoffLocation: {
    id?: string;
    name: string;
    address: string;
    time: string;
  };
  passengerInfo: {
    fullName: string;
    phoneNumber: string;
    email: string;
    agreeToTerms?: boolean;
  };
  paymentMethod: string;
  paymentStatus: string;
  bookingDate: string;
  qrCodeBase64?: string; // Thêm trường này để hiển thị QR động
}

function mapApiDataToTicketData(apiData: any, t: any, i18n: any): TicketData {
  const ticket = apiData.tickets?.[0] || apiData.external_response_data?.tickets?.[0];
  const busSchedule = ticket?.bus_schedule;
  const fleet = busSchedule?.bus?.fleet;

  // Xử lý bus type đa ngôn ngữ
  const busTypeRaw = busSchedule?.bus?.bus_type || '';
  let busType = '';
  if (busTypeRaw.toLowerCase().includes('bed') || busTypeRaw.toLowerCase().includes('giuong')) {
    busType = i18n.language === 'vi' ? 'Giường nằm' : 'Sleeper';
  } else if (busTypeRaw.toLowerCase().includes('chair') || busTypeRaw.toLowerCase().includes('ghe')) {
    busType = i18n.language === 'vi' ? 'Ghế ngồi' : 'Seater';
  } else if (busTypeRaw.toLowerCase().includes('limousine')) {
    busType = 'Limousine';
  } else {
    busType = busTypeRaw;
  }

  // Format ngày theo ngôn ngữ
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      const locale = i18n.language === 'vi' ? vi : enUS;
      if (i18n.language === 'vi') {
        const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
        const dayName = dayNames[date.getDay()];
        return `${dayName}, ${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      } else {
        return format(date, 'EEEE, MMM dd', { locale });
      }
    } catch {
      return dateStr;
    }
  };

  return {
    ticketId: apiData.order_number || ticket?.ticket_number || '',
    busCompany: fleet?.name || '',
    route: `${busSchedule?.departure_place?.name || ''} → ${ticket?.dropoff?.arrival_place?.name || ''}`,
    date: formatDate(ticket?.departure_date) || '',
    time: ticket?.departure_time || busSchedule?.departure_time || '',
    duration: ticket?.dropoff?.duration_minutes
      ? `${Math.floor(ticket.dropoff.duration_minutes / 60)} ${i18n.language === 'vi' ? 'giờ' : 'hours'} ${ticket.dropoff.duration_minutes % 60} ${i18n.language === 'vi' ? 'phút' : 'mins'}`
      : '',
    arrivalTime: ticket?.arrival_time || '',
    price: Number(ticket?.price || 0),
    quantity: ticket?.quantity || 1,
    totalPrice: Number(apiData.total_amount || 0),
    busType: busType,
    pickupLocation: {
      name: busSchedule?.departure_place?.name || '',
      address: '',
      time: ticket?.departure_time || '',
    },
    dropoffLocation: {
      name: ticket?.dropoff?.location_name || '',
      address: '',
      time: ticket?.arrival_time || '',
    },
    passengerInfo: {
      fullName: apiData.customer_name || ticket?.customer_name || '',
      phoneNumber: apiData.customer_phone || ticket?.customer_phone || '',
      email: apiData.customer_email || ticket?.customer_email || '',
    },
    paymentMethod: apiData.payment_method || '',
    paymentStatus: apiData.payment_status || apiData.external_response_data?.status || '',
    bookingDate: apiData.created_at || '',
    qrCodeBase64: apiData.payment_response_data?.vietQRImage || '',
  };
}

// Mock data chỉ dùng khi không có dữ liệu được truyền vào
const MOCK_TICKET_DATA: TicketData = {
  ticketId: 'BK12345678',
  busCompany: 'Bình Định - Đà Nẵng',
  route: 'Bình Định → Đà Nẵng',
  date: 'Thứ 4, 28/08',
  time: '17:00',
  duration: '14 giờ',
  arrivalTime: '07:00',
  price: 300000,
  quantity: 2,
  totalPrice: 600000,
  busType: 'Giường nằm',
  pickupLocation: {
    name: 'Bến xe Gia Lâm',
    address: 'Số 9 Ngô Gia Khảm, phường Gia Thụy, quận Long Biên, Hà Nội',
    time: '19:30'
  },
  dropoffLocation: {
    name: 'Bến xe Trung tâm Đà Nẵng',
    address: '201 Tôn Đức Thắng, Hoà Minh, Liên Chiểu, Đà Nẵng',
    time: '07:00'
  },
  passengerInfo: {
    fullName: 'Nguyễn Văn A',
    phoneNumber: '0987654321',
    email: '<EMAIL>'
  },
  paymentMethod: 'Thanh toán qua ví MoMo',
  paymentStatus: 'Đã thanh toán',
  bookingDate: '25/08/2023 15:30'
};

export default function TicketConfirmationScreen() {
  const { t, i18n } = useTranslation();
  const params = useLocalSearchParams();

  // Tạo mock data với translation
  const getMockTicketData = (): TicketData => ({
    ticketId: 'BK12345678',
    busCompany: 'Bình Định - Đà Nẵng',
    route: 'Bình Định → Đà Nẵng',
    date: i18n.language === 'vi' ? 'Thứ 4, 28/08' : 'Wed, Aug 28',
    time: '17:00',
    duration: i18n.language === 'vi' ? '14 giờ' : '14 hours',
    arrivalTime: '07:00',
    price: 300000,
    quantity: 2,
    totalPrice: 600000,
    busType: t('vehicleType.bed'),
    pickupLocation: {
      name: 'Bến xe Gia Lâm',
      address: 'Số 9 Ngô Gia Khảm, phường Gia Thụy, quận Long Biên, Hà Nội',
      time: '19:30'
    },
    dropoffLocation: {
      name: 'Bến xe Trung tâm Đà Nẵng',
      address: '201 Tôn Đức Thắng, Hoà Minh, Liên Chiểu, Đà Nẵng',
      time: '07:00'
    },
    passengerInfo: {
      fullName: 'Nguyễn Văn A',
      phoneNumber: '0987654321',
      email: '<EMAIL>'
    },
    paymentMethod: 'Thanh toán qua ví MoMo',
    paymentStatus: 'Đã thanh toán',
    bookingDate: '25/08/2023 15:30'
  });

  const [ticketData, setTicketData] = useState<TicketData>(getMockTicketData());
  const [qrModalVisible, setQrModalVisible] = useState(false);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes in seconds

  useEffect(() => {
    // Khi timeLeft = 0 thì chuyển về màn hình Home
    if (timeLeft === 0) {
      router.replace('/');
    }
  }, [timeLeft]);

  useEffect(() => {
    // Ưu tiên lấy dữ liệu thực tế từ params.apiData
    if (params.apiData) {
      try {
        const parsedData = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
        setTicketData(mapApiDataToTicketData(parsedData, t, i18n));
      } catch (error) {
        console.error('Error parsing API data:', error);
      }
    } else if (params.bookingData) {
      // Giữ lại fallback bookingData cũ nếu có
      try {
        const parsedData = JSON.parse(params.bookingData as string);
        setTicketData(parsedData);
      } catch (error) {
        console.error('Error parsing booking data:', error);
      }
    }
    // Bộ đếm ngược 5 phút
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  }, []);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN').format(price) + ' đ';
  };

  useEffect(() => {
    // Lấy orderId từ params hoặc ticketData tuỳ luồng của bạn
    const orderId = params?.orderId;
    if (orderId) {
      const id = Array.isArray(orderId) ? orderId[0] : orderId;
      const timeout = setTimeout(async () => {
        try {
          const data = await confirmPayment(id);
          // Lấy external_response_data và tickets
          const externalData = data.external_response_data;
          const ticketsArr = externalData?.tickets || [];
          // Truyền sang print-ticket: truyền toàn bộ mảng tickets nếu có
          router.push({
            pathname: '/print-ticket',
            params: {
              printData: JSON.stringify({
                external_response_data: externalData,
                tickets: ticketsArr.map((t: any) => ({
                  ticket_number: t.ticket_number,
                  qr_code_image: t.qr_code_image || '',
                }))
              }),
              tripData: params.tripData ? (typeof params.tripData === 'string' ? params.tripData : JSON.stringify(params.tripData)) : undefined,
              date: params.date
            }
          });
        } catch (error) {
          console.error('Error confirming payment:', error);
        }
      }, 5000);
      return () => clearTimeout(timeout);
    } else {
      // Nếu không có orderId, mới set printTimeout auto chuyển sau 10s
      const printTimeout = setTimeout(() => {
        let printDataStr = '';
        let tripDataStr = '';
        try {
          if (params.apiData) {
            // Nếu có nhiều tickets, truyền mảng tickets
            const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
            const ticketsArr = data.external_response_data?.tickets || data.tickets || [];
            printDataStr = JSON.stringify({
              ...data,
              tickets: ticketsArr.map((t: any) => ({
                ticket_number: t.ticket_number,
                qr_code_image: t.qr_code_image || '',
              }))
            });
          } else {
            printDataStr = typeof ticketData === 'string' ? ticketData : JSON.stringify(ticketData);
          }
          if (params.tripData) {
            tripDataStr = typeof params.tripData === 'string' ? params.tripData : JSON.stringify(params.tripData);
          }
        } catch (e) {
          printDataStr = '';
          tripDataStr = '';
        }
        router.push({
          pathname: '/print-ticket',
          params: {
            printData: printDataStr,
            tripData: tripDataStr,
            date: params.date
          }
        });
      }, 10000);
      return () => clearTimeout(printTimeout);
    }
  }, []);

  return (
    <PaperProvider theme={theme}>
      <StatusBar barStyle="light-content" backgroundColor="#2D5BFF" />
      <LinearGradient
        colors={['#2D5BFF', '#4A90E2', '#87CEEB', '#F0F8FF']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.headerCard}>
            <View style={styles.headerContent}>
              <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
                <MaterialIcons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Title style={styles.headerTitle}>
                {t('ticketConfirmation.title')}
              </Title>
              <View style={styles.headerDecoration}>
                <View style={styles.decorationDot} />
                <View style={[styles.decorationDot, styles.decorationDotSecondary]} />
                <View style={[styles.decorationDot, styles.decorationDotTertiary]} />
              </View>
            </View>
          </View>

          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* QR Code and Payment Info Card */}
            <View style={styles.infoCard}>
              {/* Timer Section - Moved to top */}
              <View style={styles.timerSection}>
                <MaterialIcons name="timer" size={24} color="#FF6B6B" />
                <Text style={styles.timeLeft}>
                  {t('payment.timeLeft')}: <Text style={styles.timeLeftHighlight}>{formatTime(timeLeft)}</Text>
                </Text>
              </View>

              <View style={styles.qrAndPaymentContainer}>
                {/* QR Code Section */}
                <View style={styles.qrSection}>
                  <TouchableOpacity
                    style={styles.qrCodeWrapper}
                    onPress={() => setQrModalVisible(true)}
                    activeOpacity={0.8}
                  >
                    <Image
                      source={
                        ticketData.qrCodeBase64
                          ? { uri: `data:image/png;base64,${ticketData.qrCodeBase64}` }
                          : require('../../assets/images/qr-code-sample.png')
                      }
                      style={styles.qrCode}
                      resizeMode="contain"
                    />
                    <View style={styles.tapToEnlargeContainer}>
                      <MaterialIcons name="qr-code-scanner" size={16} color="#2D5BFF" />
                      <Text style={styles.tapToEnlargeText}>
                        {t('payment.tapToEnlarge')}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                {/* Payment Info Section */}
                <View style={styles.paymentInfoSection}>
                  <View style={styles.paymentDetails}>
                    <Text style={styles.detailLabel}>{t('payment.orderCode')}</Text>
                    <Text style={styles.detailValue}>{ticketData.ticketId}</Text>

                    <Text style={styles.detailLabel}>{t('payment.bank')}</Text>
                    <Text style={styles.detailValue}>{
                      (params.apiData && (() => {
                        try {
                          const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
                          return data.payment_response_data?.extraInfo4 || '---';
                        } catch { return '---'; }
                      })()) || '---'
                    }</Text>

                    <Text style={styles.detailLabel}>{t('payment.accountHolder')}</Text>
                    <Text style={styles.detailValue}>{
                      (params.apiData && (() => {
                        try {
                          const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
                          return data.payment_response_data?.extraInfo1 || '---';
                        } catch { return '---'; }
                      })()) || '---'
                    }</Text>

                    <Text style={styles.detailLabel}>{t('payment.accountNumber') || 'Số tài khoản'}</Text>
                    <Text style={styles.detailValue}>{
                      (params.apiData && (() => {
                        try {
                          const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
                          return data.payment_response_data?.extraInfo2 || '---';
                        } catch { return '---'; }
                      })()) || '---'
                    }</Text>

                    <Text style={styles.detailLabel}>{t('payment.amount')}</Text>
                    <Text style={styles.totalAmount}>{formatPrice(ticketData.totalPrice)}</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Trip Information Card */}
            <View style={styles.infoCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="directions-bus" size={24} color="#2D5BFF" />
                <Text style={styles.cardTitle}>
                  {t('ticketConfirmation.tripInfo')}
                </Text>
              </View>

              {(() => {
                let tripData = null;
                if (params.tripData) {
                  try {
                    tripData = typeof params.tripData === 'string' ? JSON.parse(params.tripData) : params.tripData;
                  } catch { }
                }

                const infoRows = [];
                if (tripData) {
                  const bus = tripData.bus;
                  const fleet = bus?.fleet;
                  const depPlace = tripData.departure_place;
                  const dropoff = tripData.dropoffs?.[0];
                  const arrPlace = dropoff?.arrival_place;

                  if (fleet?.name) infoRows.push([t('ticketConfirmation.busCompany'), fleet.name]);
                  else if (bus?.brand) infoRows.push([t('ticketConfirmation.busCompany'), bus.brand]);
                  else if (bus?.license_plate) infoRows.push([t('ticketConfirmation.busCompany'), bus.license_plate]);

                  if (depPlace?.name && (arrPlace?.name || dropoff?.location_name)) infoRows.push([
                    t('ticketConfirmation.route'), `${depPlace.name} → ${arrPlace?.name || dropoff?.location_name}`
                  ]);

                  // Cho ngày, sử dụng ngày đã chọn nếu có, nếu không thì lấy ngày trong dữ liệu vé
                  let dateStr = '';
                  if (params.date) {
                    const d = new Date(params.date as string);
                    dateStr = format(d, 'EEEE, dd/MM/yyyy', { locale: i18n.language === 'vi' ? vi : enUS });
                  } else {
                    const today = new Date();
                    dateStr = `${today.toLocaleDateString('vi-VN', { weekday: 'short' })}, ${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}`;
                  }
                  infoRows.push([t('ticketConfirmation.date'), dateStr]);

                  if (tripData.departure_time) infoRows.push([
                    t('ticketConfirmation.time') || 'Giờ đi', tripData.departure_time.slice(0, 5)
                  ]);

                  if (tripData.arrival_time) infoRows.push([
                    t('ticketConfirmation.arrivalTime') || 'Giờ đến', tripData.arrival_time.slice(0, 5)
                  ]);

                  if (bus?.license_plate) infoRows.push([t('ticketConfirmation.licensePlate') || 'Biển số', bus.license_plate]);
                  if (bus?.bus_type) {
                    let busType = '';
                    const busTypeRaw = bus.bus_type || '';
                    if (busTypeRaw.toLowerCase().includes('bed') || busTypeRaw.toLowerCase().includes('giuong')) {
                      busType = i18n.language === 'vi' ? 'Giường nằm' : 'Sleeper bus';
                    } else if (busTypeRaw.toLowerCase().includes('chair') || busTypeRaw.toLowerCase().includes('ghe')) {
                      busType = i18n.language === 'vi' ? 'Ghế ngồi' : 'Seat';
                    } else if (busTypeRaw.toLowerCase().includes('limousine')) {
                      busType = 'Limousine';
                    } else {
                      busType = busTypeRaw;
                    }
                    infoRows.push([t('ticketConfirmation.busType') || 'Loại xe', busType]);
                  }

                  if (dropoff?.duration_minutes) {
                    const mins = dropoff.duration_minutes;
                    const duration = `${Math.floor(mins / 60)} giờ${mins % 60 ? ' ' + (mins % 60) + ' phút' : ''}`;
                    infoRows.push([t('ticketConfirmation.duration') || 'Thời gian di chuyển', duration]);
                  }
                } else {
                  // Fallback về dữ liệu order nếu không có trip data
                  const ticket = ticketData;
                  // Get bus_schedule from original API data instead of mapped ticketData
                  let busSchedule = null;
                  if (params.apiData) {
                    try {
                      const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
                      const ticket = data.tickets?.[0] || data.external_response_data?.tickets?.[0];
                      busSchedule = ticket?.bus_schedule;
                    } catch { }
                  }
                  const fleet = busSchedule?.bus?.fleet;
                  if (fleet?.name) infoRows.push([t('ticketConfirmation.busCompany'), fleet.name]);
                  else if (busSchedule?.bus?.brand) infoRows.push([t('ticketConfirmation.busCompany'), busSchedule?.bus?.brand]);
                  else if (busSchedule?.bus?.license_plate) infoRows.push([t('ticketConfirmation.busCompany'), busSchedule?.bus?.license_plate]);
                  if (busSchedule?.departure_place?.name && ticket.dropoffLocation?.name) infoRows.push([
                    t('ticketConfirmation.route'), `${busSchedule?.departure_place?.name} → ${ticket.dropoffLocation?.name}`
                  ]);
                  if (ticket?.date) infoRows.push([t('ticketConfirmation.date'), ticket.date]);
                  if (ticket?.time || busSchedule?.departure_time) infoRows.push([
                    t('ticketConfirmation.time') || 'Giờ đi', ticket?.time || busSchedule?.departure_time
                  ]);
                  if (ticket?.arrivalTime || busSchedule?.arrival_time) infoRows.push([
                    t('ticketConfirmation.arrivalTime') || 'Giờ đến', ticket?.arrivalTime || busSchedule?.arrival_time
                  ]);
                  if (busSchedule?.bus?.license_plate) infoRows.push([t('ticketConfirmation.licensePlate') || 'Biển số', busSchedule?.bus?.license_plate]);
                  if (busSchedule?.bus?.bus_type) {
                    let busType = '';
                    const busTypeRaw = busSchedule.bus.bus_type || '';
                    if (busTypeRaw.toLowerCase().includes('bed') || busTypeRaw.toLowerCase().includes('giuong')) {
                      busType = i18n.language === 'vi' ? 'Giường nằm' : 'Sleeper';
                    } else if (busTypeRaw.toLowerCase().includes('chair') || busTypeRaw.toLowerCase().includes('ghe')) {
                      busType = i18n.language === 'vi' ? 'Ghế ngồi' : 'Seater';
                    } else if (busTypeRaw.toLowerCase().includes('limousine')) {
                      busType = 'Limousine';
                    } else {
                      busType = busTypeRaw;
                    }
                    infoRows.push([t('ticketConfirmation.busType') || 'Loại xe', busType]);
                  }
                  if (params.apiData) {
                    try {
                      const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
                      const apiTicket = data.tickets?.[0] || data.external_response_data?.tickets?.[0];
                      if (apiTicket?.dropoff?.duration_minutes) {
                        const mins = apiTicket.dropoff.duration_minutes;
                        const duration = `${Math.floor(mins / 60)} giờ${mins % 60 ? ' ' + (mins % 60) + ' phút' : ''}`;
                        infoRows.push([t('ticketConfirmation.duration') || 'Thời gian di chuyển', duration]);
                      }
                    } catch { }
                  }
                  if (tripData.departure_time) infoRows.push([
                    t('ticketConfirmation.ticketPrice') || 'Giá vé',
                    tripData?.dropoffs?.[0]?.ticket_price ? `${tripData.dropoffs[0].ticket_price.toLocaleString()} đ` : ''
                  ]);

                  // Thêm số lượng vé
                  if (params.apiData) {
                    try {
                      const data = typeof params.apiData === 'string' ? JSON.parse(params.apiData) : params.apiData;
                      const apiTicket = data.tickets?.[0] || data.external_response_data?.tickets?.[0];
                      const quantity = apiTicket?.quantity || 1;
                      infoRows.push([t('ticketConfirmation.quantity') || 'Số lượng vé', quantity.toString()]);

                      // Thêm tổng tiền
                      const totalAmount = data.total_amount || (apiTicket?.dropoff?.ticket_price * quantity);
                      if (totalAmount) {
                        infoRows.push([t('ticketConfirmation.totalAmount') || 'Tổng tiền', `${totalAmount.toLocaleString()} đ`]);
                      }
                    } catch { }
                  }
                }

                return infoRows.map(([label, value], idx) => (
                  <View style={styles.infoRow} key={idx}>
                    <Text style={styles.infoLabel}>{label}:</Text>
                    <Text style={styles.infoValue}>{value}</Text>
                  </View>
                ));
              })()}
            </View>

            {/* Passenger Information Card */}
            <View style={styles.infoCard}>
              <View style={styles.cardHeader}>
                <MaterialIcons name="person" size={24} color="#2D5BFF" />
                <Text style={styles.cardTitle}>
                  {t('ticketConfirmation.passengerInfo')}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>{t('ticketConfirmation.fullName')}:</Text>
                <Text style={styles.infoValue}>{ticketData.passengerInfo.fullName}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>{t('ticketConfirmation.phoneNumber')}:</Text>
                <Text style={styles.infoValue}>{ticketData.passengerInfo.phoneNumber}</Text>
              </View>

              {ticketData.passengerInfo.email ? (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>{t('ticketConfirmation.email')}:</Text>
                  <Text style={styles.infoValue}>{ticketData.passengerInfo.email}</Text>
                </View>
              ) : null}
            </View>
          </ScrollView>

          {/* QR Code Modal */}
          <RNModal
            visible={qrModalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setQrModalVisible(false)}
          >
            <TouchableWithoutFeedback onPress={() => setQrModalVisible(false)}>
              <View style={styles.modalOverlay}>
                <TouchableWithoutFeedback>
                  <Surface style={styles.modalContent} elevation={5}>
                    <View style={styles.modalHeader}>
                      <Text style={styles.modalTitle}>{t('payment.scanQRCode')}</Text>
                      <TouchableOpacity
                        style={styles.closeButton}
                        onPress={() => setQrModalVisible(false)}
                      >
                        <MaterialIcons name="close" size={24} color="#333" />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.enlargedQRContainer}>
                      <Image
                        source={
                          ticketData.qrCodeBase64
                            ? { uri: `data:image/png;base64,${ticketData.qrCodeBase64}` }
                            : require('../../assets/images/qr-code-sample.png')
                        }
                        style={styles.enlargedQR}
                        resizeMode="contain"
                      />
                    </View>

                    <View style={styles.modalFooter}>
                      <Text style={styles.modalFooterText}>
                        {t('payment.scanInstructions')}
                      </Text>
                    </View>
                  </Surface>
                </TouchableWithoutFeedback>
              </View>
            </TouchableWithoutFeedback>
          </RNModal>
        </View>
      </LinearGradient>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 40,
  },
  headerCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 24,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    flex: 1,
    marginHorizontal: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  headerDecoration: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  decorationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 2,
    opacity: 0.8,
  },
  decorationDotSecondary: {
    backgroundColor: '#FF6B6B',
  },
  decorationDotTertiary: {
    backgroundColor: '#4ECDC4',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  successBanner: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    alignItems: 'center',
    marginBottom: 16,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  successIconContainer: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E293B',
    marginBottom: 8,
  },
  ticketId: {
    fontSize: 16,
    color: '#64748B',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 20,
    marginBottom: 16,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1E293B',
    marginLeft: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 15,
    color: '#64748B',
    flex: 1,
  },
  infoValue: {
    fontSize: 15,
    color: '#1E293B',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  totalPrice: {
    color: '#2D5BFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  statusPaid: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  paymentDivider: {
    height: 1,
    backgroundColor: '#F1F5F9',
    marginVertical: 12,
  },
  locationSection: {
    marginBottom: 16,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginLeft: 8,
  },
  locationName: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1E293B',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 4,
  },
  locationTime: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2D5BFF',
  },
  locationDivider: {
    height: 1,
    backgroundColor: '#F1F5F9',
    marginVertical: 16,
  },
  actionButtons: {
    marginTop: 8,
    marginBottom: 16,
  },
  actionButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 8,
  },
  fullWidthButton: {
    width: '100%',
  },
  // Thêm các styles mới
  qrAndPaymentContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16, // Chỉ giữ padding ngang
    paddingBottom: 16, // Giữ padding dưới
  },
  qrSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrCodeWrapper: {
    padding: 12,
    borderWidth: 2,
    borderColor: '#2D5BFF',
    borderRadius: 16,
    backgroundColor: '#fff',
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  qrCode: {
    width: 150,
    height: 150,
  },
  paymentInfoSection: {
    flex: 1,
    paddingLeft: 16,
  },
  timerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
    // Bỏ marginTop và marginHorizontal để timer sát với đầu card
  },
  timeLeft: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  timeLeftHighlight: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  paymentDetails: {
    backgroundColor: 'rgba(45, 91, 255, 0.05)',
    padding: 16,
    borderRadius: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginBottom: 12,
  },
  totalAmount: {
    fontSize: 20,
    color: '#2D5BFF',
    fontWeight: 'bold',
  },
  tapToEnlargeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    backgroundColor: 'rgba(45, 91, 255, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tapToEnlargeText: {
    fontSize: 12,
    color: '#2D5BFF',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  enlargedQRContainer: {
    alignItems: 'center',
    padding: 20,
  },
  enlargedQR: {
    width: 250,
    height: 250,
  },
  modalFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  modalFooterText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});



