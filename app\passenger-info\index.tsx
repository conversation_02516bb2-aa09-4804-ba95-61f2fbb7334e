import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import {
  MD3LightTheme,
  Provider as PaperProvider,
  Text,
  TextInput,
  Title
} from 'react-native-paper';

// Import PickupDropoffModal
import { format } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { createOrder } from '../../api/orderService';
import TripInfoModal from '../../components/trip/TripInfoModal';

const { width, height } = Dimensions.get('window');
const isLargeScreen = width >= 1200; // breakpoint cho kiosk

// Custom theme for React Native Paper - Synchronized with Welcome page
const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#2D5BFF',
    secondary: '#FF6B6B',
    tertiary: '#4ECDC4',
    surface: '#FFFFFF',
    background: '#F8FAFF',
    surfaceVariant: 'rgba(255, 255, 255, 0.8)',
  },
};

// Mock data cho chuyến xe đã chọn
const MOCK_TRIP_DATA = {
  busCompany: 'Bình Định - Đà Nẵng',
  route: 'Bình Định → Đà Nẵng',
  date: 'Thứ 4, 28/08',
  time: '17:00',
  duration: '14 giờ',
  arrivalTime: '07:00',
  price: 300000,
  busType: 'The New Way',
  departureLocation: 'Quảng Phú (Cư Mgar)',
  arrivalLocation: 'BX Trung tâm Đà Nẵng',
  features: ['Wifi miễn phí', 'Điều hòa', 'Ghế nằm', 'Nước uống']
};

interface PassengerInfo {
  fullName: string;
  phoneNumber: string;
  email: string;
  agreeToTerms: boolean;
}

export default function PassengerInfoScreen() {
  const { t, i18n } = useTranslation();
  const params = useLocalSearchParams();
  const trip = params.trip ? JSON.parse(params.trip as string) : null;
  const numTickets = Number(params.numTickets) || 1;

  // Lấy ngày từ params (nếu có), parse sang Date
  const selectedDate = params.date ? new Date(params.date as string) : new Date();
  const date = format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: i18n.language === 'vi' ? vi : enUS });

  // Lấy dữ liệu thực tế từ trip object
  const busCompany = trip?.bus?.fleet?.name || '';
  const route = `${trip?.departure_place?.name || ''} → ${trip?.dropoffs?.[0]?.arrival_place?.name || trip?.dropoffs?.[0]?.location_name || ''}`;
  const time = trip?.departure_time?.slice(0, 5) || '';
  const arrivalTime = trip?.arrival_time?.slice(0, 5) || '';
  let duration = '';
  if (trip?.departure_time && trip?.arrival_time) {
    const [h1, m1] = trip.departure_time.split(':').map(Number);
    const [h2, m2] = trip.arrival_time.split(':').map(Number);
    let mins = (h2 * 60 + m2) - (h1 * 60 + m1);
    if (mins < 0) mins += 24 * 60;
    duration = `${Math.floor(mins / 60)} giờ${mins % 60 ? ` ${mins % 60} phút` : ''}`;
  }
  const price = trip?.price ?? (trip?.dropoffs?.[0]?.ticket_price ?? null);
  const busTypeRaw = trip?.bus?.bus_type || trip?.bus?.model || '';
  let busType = '';
  if (busTypeRaw.toLowerCase().includes('bed') || busTypeRaw.toLowerCase().includes('giuong')) {
    busType = t('vehicleType.bed');
  } else if (busTypeRaw.toLowerCase().includes('chair') || busTypeRaw.toLowerCase().includes('ghe')) {
    busType = t('vehicleType.chair');
  } else if (busTypeRaw.toLowerCase().includes('limousine')) {
    busType = t('vehicleType.limousine');
  } else {
    busType = busTypeRaw;
  }
  const departureLocation = trip?.departure_place?.name || '';
  const arrivalLocation = trip?.dropoffs?.[0]?.arrival_place?.name || trip?.dropoffs?.[0]?.location_name || '';
  const features = trip?.bus?.description ? [trip.bus.description] : [];

  const maxTickets = trip?.bus?.seats ?? 1;
  const [ticketQuantity, setTicketQuantity] = useState(1);
  const [passengerInfo, setPassengerInfo] = useState<PassengerInfo>({
    fullName: '',
    phoneNumber: '',
    email: '',
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState<{ fullName?: string; phoneNumber?: string; email?: string }>({});
  const [showTermsModal, setShowTermsModal] = useState(false);

  const handleQuantityChange = (increment: boolean) => {
    if (increment && ticketQuantity < maxTickets) {
      setTicketQuantity(ticketQuantity + 1);
    } else if (!increment && ticketQuantity > 1) {
      setTicketQuantity(ticketQuantity - 1);
    }
  };

  // Validate từng trường
  const validateField = (field: keyof PassengerInfo, value: string) => {
    switch (field) {
      case 'fullName':
        if (!value.trim()) return t('passengerInfo.errorNameRequired');
        if (value.trim().length < 2) return t('passengerInfo.errorNameTooShort');
        if (value.trim().length > 50) return t('passengerInfo.errorNameTooLong');
        return '';
      case 'phoneNumber':
        if (!value.trim()) return t('passengerInfo.errorPhoneRequired');
        if (i18n.language === 'vi') {
          if (!/^(0|\+84)([3|5|7|8|9])([0-9]{8})$/.test(value.trim())) 
            return t('passengerInfo.errorPhoneInvalid');
        } else {
          if (!/^\+?[0-9]{9,15}$/.test(value.trim())) 
            return t('passengerInfo.errorPhoneInvalid');
        }
        return '';
      case 'email':
        if (!value.trim()) return '';
        if (value.trim().length > 100) return t('passengerInfo.errorEmailTooLong');
        if (!/^\S+@\S+\.\S+$/.test(value.trim())) return t('passengerInfo.errorEmailInvalid');
        return '';
      default:
        return '';
    }
  };

  const handleInputChange = (field: keyof PassengerInfo, value: string | boolean) => {
    setPassengerInfo(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Nếu đang có lỗi và giá trị mới hợp lệ, xóa thông báo lỗi
    if (errors[field as keyof typeof errors] && typeof value === 'string') {
      const errorMessage = validateField(field as 'fullName' | 'phoneNumber' | 'email', value);
      if (!errorMessage) {
        setErrors(prev => ({
          ...prev,
          [field]: undefined
        }));
      }
    }
  };

  const isFormValid = () => {
    const hasName = !!passengerInfo.fullName.trim();
    const hasPhone = !!passengerInfo.phoneNumber.trim();
    const hasAgreed = passengerInfo.agreeToTerms;
    return hasName && hasPhone && hasAgreed;
  };

  const handleBooking = async () => {
    // Kiểm tra validation khi người dùng bấm nút
    const nameError = validateField('fullName', passengerInfo.fullName);
    const phoneError = validateField('phoneNumber', passengerInfo.phoneNumber);
    // Chỉ kiểm tra định dạng email nếu người dùng có nhập
    const emailError = passengerInfo.email.trim() ? validateField('email', passengerInfo.email) : '';

    setErrors({
      fullName: nameError,
      phoneNumber: phoneError,
      email: emailError
    });

    if (!nameError && !phoneError && !emailError && passengerInfo.agreeToTerms) {
      // Build body đúng format API
      const d = params.date ? new Date(params.date as string) : new Date();
      const dateApiFormat = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
      const orderData = {
        payment_method: "Cash",
        customer_name: passengerInfo.fullName,
        customer_phone: passengerInfo.phoneNumber,
        tickets: [
          {
            bus_schedule_id: trip.id,
            dropoff_id: trip.dropoffs?.[0]?.id,
            departure_date: dateApiFormat, 
            quantity: ticketQuantity
          }
        ]
      };
      if (passengerInfo.email && passengerInfo.email.trim() !== "") {
        (orderData as any).customer_email = passengerInfo.email.trim();
      }
      try {
        const res = await createOrder(orderData);
        router.push({
          pathname: '/ticket',
          params: {
            orderId: res.data.id,
            apiData: JSON.stringify(res.data),
            tripData: JSON.stringify(trip),
            date: params.date // truyền ngày đã chọn sang ticket
          }
        });
      } catch (err) {
        console.log("Error:",err)
      }
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  return (
    <PaperProvider theme={theme}>
      <StatusBar barStyle="light-content" backgroundColor="#2D5BFF" />
      <LinearGradient
        colors={['#2D5BFF', '#4A90E2', '#87CEEB', '#F0F8FF']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView 
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.container}
          keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
        >
          {/* Header */}
          <View style={styles.headerCard}>
            <View style={styles.headerContent}>
              <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
                <MaterialIcons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Title style={styles.headerTitle}>
                {t('passengerInfo.title')}
              </Title>
              <View style={styles.headerDecoration}>
                <View style={styles.decorationDot} />
                <View style={[styles.decorationDot, styles.decorationDotSecondary]} />
                <View style={[styles.decorationDot, styles.decorationDotTertiary]} />
              </View>
            </View>
          </View>

          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            {/* Layout 2 cột cho màn hình lớn: chỉ tripCard và quantityCard */}
            {isLargeScreen ? (
              <View style={{ flexDirection: 'row', gap: 24, alignItems: 'stretch', marginBottom: 24 }}>
                {/* Cột trái: Thông tin chuyến đi */}
                <View style={{ flex: 1, minWidth: 0 }}>
                  <View style={[styles.tripCard, styles.cardLargeScreen]}>
                    <View style={styles.tripCardContent}>
                      <View style={styles.tripHeader}>
                        <View style={styles.companyInfo}>
                          <View style={styles.companyBadge}>
                            <MaterialIcons name="directions-bus" size={20} color="#FFFFFF" />
                            <Text style={styles.companyName}>{busCompany}</Text>
                          </View>
                          <Text style={styles.busType}>{busType}</Text>
                        </View>
                        <View style={styles.priceContainer}>  
                          <Text style={styles.priceLabel}>{t('passengerInfo.ticketPrice')}</Text>
                          <View style={styles.priceWrapper}>
                            <Text style={styles.price}>{formatPrice(price)}</Text>
                            <View style={styles.priceBadge}>
                              <Text style={styles.priceBadgeText}>VND</Text>
                            </View>
                          </View>
                        </View>
                      </View>

                      <View style={styles.routeContainer}>
                        <View style={styles.routeInfo2Col}>
                          {/* Cột trái: Giờ khởi hành + chấm + điểm đi */}
                          <View style={styles.routeCol}>
                            <Text style={styles.time}>{time}</Text>
                            <View style={styles.timeIndicator} />
                            <Text style={styles.location}>{departureLocation}</Text>
                          </View>
                          {/* Phần giữa: duration ngang */}
                          <View style={styles.routeDurationCol}>
                            <View style={styles.routeDotsHorizontal}>
                              <View style={styles.routeDot} />
                              <View style={styles.routeDot} />
                              <View style={styles.routeDot} />
                            </View>
                            <Text style={styles.durationVertical}>{duration}</Text>
                          </View>
                          {/* Cột phải: Giờ đến + chấm + điểm đến */}
                          <View style={styles.routeCol}>
                            <Text style={styles.time}>{arrivalTime}</Text>
                            <View style={[styles.timeIndicator, styles.timeIndicatorEnd]} />
                            <Text style={styles.location}>{arrivalLocation}</Text>
                          </View>
                        </View>
                      </View>

                      <View style={styles.dateContainer}>
                        <MaterialIcons name="event" size={18} color="#2D5BFF" />
                        <Text style={styles.dateInfo}>{date}</Text>
                      </View>
                    </View>
                  </View>
                </View>
                {/* Cột phải: Số lượng vé */}
                <View style={{ flex: 1, minWidth: 0 }}>
                  <View style={[styles.quantityCard, styles.cardLargeScreen]}>
                    <View style={styles.quantityCardContent}>
                      <View style={styles.sectionHeader}>
                        <MaterialIcons name="confirmation-number" size={24} color="#2D5BFF" />
                        <Text style={styles.sectionTitle}>{t('passengerInfo.ticketQuantity')}</Text>
                      </View>

                      <View style={styles.quantitySelector}>
                        <TouchableOpacity
                          style={[styles.quantityButton, ticketQuantity <= 1 && styles.quantityButtonDisabled]}
                          onPress={() => handleQuantityChange(false)}
                          disabled={ticketQuantity <= 1}
                        >
                          <MaterialIcons
                            name="remove"
                            size={28}
                            color={ticketQuantity <= 1 ? '#CBD5E1' : '#FFFFFF'}
                          />
                        </TouchableOpacity>

                        <View style={styles.quantityDisplay}>
                          <Text style={styles.quantityText}>{ticketQuantity}</Text>
                          <Text style={styles.quantityLabel}>{t('passengerInfo.tickets')}</Text>
                        </View>

                        <TouchableOpacity
                          style={[styles.quantityButton, ticketQuantity >= maxTickets && styles.quantityButtonDisabled]}
                          onPress={() => handleQuantityChange(true)}
                          disabled={ticketQuantity >= maxTickets}
                        >
                          <MaterialIcons
                            name="add"
                            size={28}
                            color={ticketQuantity >= maxTickets ? '#CBD5E1' : '#FFFFFF'}
                          />
                        </TouchableOpacity>
                      </View>

                      <View style={styles.totalPriceContainer}>
                        <View style={styles.totalPriceWrapper}>
                          <Text style={styles.totalPriceLabel}>{t('passengerInfo.totalPayment')}</Text>
                          <View style={styles.totalPriceDisplay}>
                            <Text style={styles.totalPrice}>
                              {formatPrice(price * ticketQuantity)}
                            </Text>
                            <View style={styles.totalPriceBadge}>
                              <MaterialIcons name="payments" size={16} color="#4ECDC4" />
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            ) : (
              <>
                <View style={styles.tripCard}>
                  <View style={styles.tripCardContent}>
                    <View style={styles.tripHeader}>
                      <View style={styles.companyInfo}>
                        <View style={styles.companyBadge}>
                          <MaterialIcons name="directions-bus" size={20} color="#FFFFFF" />
                          <Text style={styles.companyName}>{busCompany}</Text>
                        </View>
                        <Text style={styles.busType}>{busType}</Text>
                      </View>
                      <View style={styles.priceContainer}>  
                        <Text style={styles.priceLabel}>{t('passengerInfo.ticketPrice')}</Text>
                        <View style={styles.priceWrapper}>
                          <Text style={styles.price}>{formatPrice(price)}</Text>
                          <View style={styles.priceBadge}>
                            <Text style={styles.priceBadgeText}>VND</Text>
                          </View>
                        </View>
                      </View>
                    </View>

                    <View style={styles.routeContainer}>
                      <View style={styles.routeInfo2Col}>
                        {/* Cột trái: Giờ khởi hành + chấm + điểm đi */}
                        <View style={styles.routeCol}>
                          <Text style={styles.time}>{time}</Text>
                          <View style={styles.timeIndicator} />
                          <Text style={styles.location}>{departureLocation}</Text>
                        </View>
                        {/* Phần giữa: duration ngang */}
                        <View style={styles.routeDurationCol}>
                          <View style={styles.routeDotsHorizontal}>
                            <View style={styles.routeDot} />
                            <View style={styles.routeDot} />
                            <View style={styles.routeDot} />
                          </View>
                          <Text style={styles.durationVertical}>{duration}</Text>
                        </View>
                        {/* Cột phải: Giờ đến + chấm + điểm đến */}
                        <View style={styles.routeCol}>
                          <Text style={styles.time}>{arrivalTime}</Text>
                          <View style={[styles.timeIndicator, styles.timeIndicatorEnd]} />
                          <Text style={styles.location}>{arrivalLocation}</Text>
                        </View>
                      </View>
                    </View>

                    <View style={styles.dateContainer}>
                      <MaterialIcons name="event" size={18} color="#2D5BFF" />
                      <Text style={styles.dateInfo}>{date}</Text>
                    </View>
                  </View>
                </View>
                <View style={styles.quantityCard}>
                  <View style={styles.quantityCardContent}>
                    <View style={styles.sectionHeader}>
                      <MaterialIcons name="confirmation-number" size={24} color="#2D5BFF" />
                      <Text style={styles.sectionTitle}>{t('passengerInfo.ticketQuantity')}</Text>
                    </View>

                    <View style={styles.quantitySelector}>
                      <TouchableOpacity
                        style={[styles.quantityButton, ticketQuantity <= 1 && styles.quantityButtonDisabled]}
                        onPress={() => handleQuantityChange(false)}
                        disabled={ticketQuantity <= 1}
                      >
                        <MaterialIcons
                          name="remove"
                          size={28}
                          color={ticketQuantity <= 1 ? '#CBD5E1' : '#FFFFFF'}
                        />
                      </TouchableOpacity>

                      <View style={styles.quantityDisplay}>
                        <Text style={styles.quantityText}>{ticketQuantity}</Text>
                        <Text style={styles.quantityLabel}>{t('passengerInfo.tickets')}</Text>
                      </View>

                      <TouchableOpacity
                        style={[styles.quantityButton, ticketQuantity >= maxTickets && styles.quantityButtonDisabled]}
                        onPress={() => handleQuantityChange(true)}
                        disabled={ticketQuantity >= maxTickets}
                      >
                        <MaterialIcons
                          name="add"
                          size={28}
                          color={ticketQuantity >= maxTickets ? '#CBD5E1' : '#FFFFFF'}
                        />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.totalPriceContainer}>
                      <View style={styles.totalPriceWrapper}>
                        <Text style={styles.totalPriceLabel}>{t('passengerInfo.totalPayment')}</Text>
                        <View style={styles.totalPriceDisplay}>
                          <Text style={styles.totalPrice}>
                            {formatPrice(price * ticketQuantity)}
                          </Text>
                          <View style={styles.totalPriceBadge}>
                            <MaterialIcons name="payments" size={16} color="#4ECDC4" />
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </>
            )}

            {/* Form thông tin hành khách và nút đặt vé luôn nằm dưới, chiếm toàn bộ chiều ngang */}
            <View style={styles.formCard}>
              <View style={styles.formCardContent}>
                <View style={styles.sectionHeader}>
                  <MaterialIcons name="person" size={24} color="#2D5BFF" />
                  <Text style={styles.sectionTitle}>{t('passengerInfo.tripDetails')}</Text>
                </View>

                {/* Full Name Input */}
                <View style={styles.inputContainer}>
                  <View style={styles.inputLabelContainer}>
                    <MaterialIcons name="badge" size={18} color="#2D5BFF" />
                    <Text style={styles.inputLabel}>
                      {t('passengerInfo.passengerName')} <Text style={styles.redStar}>*</Text>
                    </Text>
                  </View>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      mode="outlined"
                      value={passengerInfo.fullName}
                      onChangeText={(text) => handleInputChange('fullName', text)}
                      placeholder={t('passengerInfo.enterName')}
                      style={styles.textInput}
                      contentStyle={styles.textInputContent}
                      outlineStyle={styles.textInputOutline}
                      theme={{
                        colors: {
                          primary: '#2D5BFF',
                          outline: errors.fullName ? '#FF6B6B' : 'rgba(45, 91, 255, 0.2)',
                        }
                      }}
                    />
                    {!!errors.fullName && (
                      <Text style={{ color: 'red', marginTop: 4, fontSize: 13 }}>{errors.fullName}</Text>
                    )}
                  </View>
                </View>

                {/* Phone Number Input */}
                <View style={styles.inputContainer}>
                  <View style={styles.inputLabelContainer}>
                    <MaterialIcons name="phone" size={18} color="#2D5BFF" />
                    <Text style={styles.inputLabel}>
                      {t('passengerInfo.phoneNumber')} <Text style={styles.redStar}>*</Text>
                    </Text>
                  </View>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      mode="outlined"
                      value={passengerInfo.phoneNumber}
                      onChangeText={(text) => handleInputChange('phoneNumber', text)}
                      placeholder={t('passengerInfo.enterPhone')}
                      keyboardType="phone-pad"
                      style={styles.textInput}
                      contentStyle={styles.textInputContent}
                      outlineStyle={styles.textInputOutline}
                      theme={{
                        colors: {
                          primary: '#2D5BFF',
                          outline: errors.phoneNumber ? '#FF6B6B' : 'rgba(45, 91, 255, 0.2)',
                        }
                      }}
                    />
                    {!!errors.phoneNumber && (
                      <Text style={{ color: 'red', marginTop: 4, fontSize: 13 }}>{errors.phoneNumber}</Text>
                    )}
                  </View>
                </View>

                {/* Email Input */}
                <View style={styles.inputContainer}>
                  <View style={styles.inputLabelContainer}>
                    <MaterialIcons name="email" size={18} color="#2D5BFF" />
                    <Text style={styles.inputLabel}>
                      {t('passengerInfo.email')} 
                    </Text>
                  </View>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      mode="outlined"
                      value={passengerInfo.email}
                      onChangeText={(text) => handleInputChange('email', text)}
                      placeholder={t('passengerInfo.enterEmail')}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      style={styles.textInput}
                      contentStyle={styles.textInputContent}
                      outlineStyle={styles.textInputOutline}
                      theme={{
                        colors: {
                          primary: '#2D5BFF',
                          outline: errors.email ? '#FF6B6B' : 'rgba(45, 91, 255, 0.2)',
                        }
                      }}
                    />
                    {!!errors.email && (
                      <Text style={{ color: 'red', marginTop: 4, fontSize: 13 }}>{errors.email}</Text>
                    )}
                  </View>
                </View>

                {/* Terms and Conditions Checkbox */}
                <View style={styles.checkboxContainer}>
                  <TouchableOpacity
                    style={styles.customCheckbox}
                    onPress={() => handleInputChange('agreeToTerms', !passengerInfo.agreeToTerms)}
                  >
                    <View style={[styles.checkboxBox, passengerInfo.agreeToTerms && styles.checkboxBoxChecked]}>
                      {passengerInfo.agreeToTerms && (
                        <MaterialIcons name="check" size={16} color="#FFFFFF" />
                      )}
                    </View>
                    <Text style={styles.checkboxLabel}>
                      {t('passengerInfo.agreeToTerms')}{' '}
                      <Text style={styles.linkText} onPress={() => setShowTermsModal(true)}>
                        {t('passengerInfo.termsAndConditions')}
                      </Text>
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <TouchableOpacity
              onPress={handleBooking}
              disabled={!isFormValid()}
              style={[
                styles.bookingButton, 
                !isFormValid() && styles.bookingButtonDisabled,
                isLargeScreen && styles.kioskBookingButton
              ]}
            >
              <LinearGradient
                colors={isFormValid() ? ['#2D5BFF', '#4A90E2'] : ['#CBD5E1', '#94A3B8']}
                style={styles.bookingButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <View style={styles.bookingButtonContent}>
                  <MaterialIcons
                    name="confirmation-number"
                    size={isLargeScreen ? 32 : 24}
                    color="#FFFFFF"
                    style={styles.bookingButtonIcon}
                  />
                  <Text style={[styles.bookingButtonLabel, isLargeScreen && styles.kioskButtonText]}>
                    {t('passengerInfo.bookNow')}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </ScrollView>
        </KeyboardAvoidingView>
        <TripInfoModal
          visible={showTermsModal}
          onClose={() => setShowTermsModal(false)}
          type="terms"
        />
      </LinearGradient>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 40,
  },
  headerCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 24,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    flex: 1,
    marginHorizontal: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  headerDecoration: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  decorationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 2,
    opacity: 0.8,
  },
  decorationDotSecondary: {
    backgroundColor: '#FF6B6B',
  },
  decorationDotTertiary: {
    backgroundColor: '#4ECDC4',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },

  // Trip Card Styles - Modern Design
  tripCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 24,
    marginBottom: 14,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 8,
  },
  tripCardContent: {
    padding: 16,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  companyInfo: {
    flex: 1,
  },
  companyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2D5BFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  companyName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  busType: {
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500',
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceLabel: {
    fontSize: 12,
    color: '#64748B',
    marginBottom: 4,
    fontWeight: '500',
  },
  priceWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 22,
    fontWeight: '800',
    color: '#2D5BFF',
    marginRight: 8,
  },
  priceBadge: {
    backgroundColor: '#F1F5F9',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priceBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#2D5BFF',
  },
  routeContainer: {
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  routeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  timePoint: {
    alignItems: 'center',
  },
  time: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  timeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#2D5BFF',
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  timeIndicatorEnd: {
    backgroundColor: '#4ECDC4',
  },
  routeLine: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  routeDots: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  routeDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#CBD5E1',
    marginHorizontal: 2,
  },
  duration: {
    fontSize: 12,
    color: '#64748B',
    fontWeight: '500',
    backgroundColor: '#F1F5F9',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  routeDetails: {
    flex: 1,
    alignItems: 'flex-end',
  },
  location: {
    fontSize: 14,
    color: '#475569',
    textAlign: 'center',
    marginTop: 8,
    fontWeight: '500',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  dateInfo: {
    fontSize: 14,
    color: '#2D5BFF',
    fontWeight: '600',
    marginLeft: 6,
  },

  // Quantity Card Styles - Modern Design
  quantityCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 24,
    marginBottom: 14,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 8,
  },
  quantityCardContent: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1E293B',
    marginLeft: 12,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    gap: 24,
  },
  quantityButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#2D5BFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  quantityButtonDisabled: {
    backgroundColor: '#E2E8F0',
    shadowOpacity: 0,
    elevation: 0,
  },
  quantityDisplay: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E2E8F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  quantityText: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1E293B',
  },
  quantityLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#64748B',
    marginTop: 2,
  },
  totalPriceContainer: {
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    padding: 16,
    marginTop: 8,
  },
  totalPriceWrapper: {
    alignItems: 'center',
  },
  totalPriceLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginBottom: 8,
  },
  totalPriceDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalPrice: {
    fontSize: 24,
    fontWeight: '800',
    color: '#4ECDC4',
    marginRight: 8,
  },
  totalPriceBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#DCFCE7',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Form Card Styles - Modern Design
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 24,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 8,
  },
  formCardContent: {
    padding: 16,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: isLargeScreen ? 18 : 14,
    fontWeight: '600',
    color: '#1E293B',
    marginLeft: 8,
  },

  redStar: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },

  inputWrapper: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  textInput: {
    backgroundColor: '#FFFFFF',
    fontSize: 22,
    height: 64,
    borderRadius: 20,
  },
  textInputContent: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  textInputOutline: {
    borderRadius: 16,
    borderWidth: 1.5,
  },
  checkboxContainer: {
    marginTop: 16,
  },
  customCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxBox: {
    width: 24,
    height: 24,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkboxBoxChecked: {
    backgroundColor: '#2D5BFF',
    borderColor: '#2D5BFF',
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    flex: 1,
  },
  linkText: {
    color: '#2D5BFF',
    fontWeight: '600',
  },

  // Booking Button Styles - Modern Design
  bookingButton: {
    borderRadius: 24,
    marginHorizontal: 16,
    marginBottom: 20,
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
    overflow: 'hidden',
  },
  bookingButtonDisabled: {
    shadowOpacity: 0,
    elevation: 0,
  },
  bookingButtonGradient: {
    borderRadius: 24,
  },
  bookingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 32,
  },
  bookingButtonIcon: {
    marginRight: 12,
  },
  bookingButtonLabel: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  cardLargeScreen: {
    flex: 1,
    minHeight: 280,
    alignSelf: 'stretch',
  },
  kioskQuantityButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  kioskBookingButton: {
    width: '100%',
    height: 72,
    borderRadius: 36,
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  kioskButtonText: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  kioskInput: {
    height: 64,
    fontSize: 20,
    paddingHorizontal: 20,
    borderRadius: 16,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: isLargeScreen ? 16 : 12,
    marginTop: 4,
  },
  // Thêm/Chỉnh sửa style cho layout 2 cột
  routeInfo2Col: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  routeCol: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  routeDurationCol: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  durationVertical: {
    fontSize: 12,
    color: '#64748B',
    fontWeight: '500',
    backgroundColor: '#F1F5F9',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    textAlign: 'center',
    marginTop: 4,
  },
  routeDotsHorizontal: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
    gap: 4,
  },
});
