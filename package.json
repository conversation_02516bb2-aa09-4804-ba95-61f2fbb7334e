{"name": "booking-ticket", "main": "expo-router/entry", "version": "1.0.0", "description": "Booking ticket app", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@miblanchard/react-native-slider": "^2.6.0", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "autoprefixer": "^10.4.16", "axios": "^1.10.0", "date-fns": "^4.1.0", "expo": "53.0.19", "expo-blur": "~14.1.5", "expo-constants": "17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linear-gradient": "^14.1.5", "expo-linking": "7.1.7", "expo-router": "~5.1.2", "expo-splash-screen": "0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18next": "^23.0.1", "nativewind": "^4.1.23", "postcss": "^8.4.38", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^13.0.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-ui-lib": "^7.44.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.1", "expo-av": "~15.1.7", "react-native-svg": "15.11.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}