import { MaterialIcons } from '@expo/vector-icons';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Dimensions, FlatList, Modal, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

interface Place {
  id: number;
  name: string;
}

interface PlaceSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  places: Place[];
  initialFromId?: number;
  initialToId?: number;
  onConfirm: (fromId: number, toId: number) => void;
  onlySelectTo?: boolean; // Thêm prop này
}

function removeDiacritics(str: string) {
  return str.normalize('NFD').replace(/\p{Diacritic}/gu, '').replace(/đ/g, 'd').replace(/Đ/g, 'D');
}

const PlaceSelectionModal = ({ visible, onClose, places, initialFromId, initialToId, onConfirm, onlySelectTo }: PlaceSelectionModalProps) => {
  const { t } = useTranslation();
  const [fromSearch, setFromSearch] = useState('');
  const [toSearch, setToSearch] = useState('');
  const [selectedFrom, setSelectedFrom] = useState<number | undefined>(initialFromId);
  const [selectedTo, setSelectedTo] = useState<number | undefined>(initialToId);
  const [fromInputFocused, setFromInputFocused] = useState(false);
  const [toInputFocused, setToInputFocused] = useState(false);
  const [hasConfirmed, setHasConfirmed] = useState(false);

  const filteredFrom = useMemo(() =>
    places.filter(p =>
      removeDiacritics(p.name.toLowerCase()).includes(removeDiacritics(fromSearch.toLowerCase()))
    ),
    [places, fromSearch]
  );
  const filteredTo = useMemo(() =>
    places.filter(p =>
      removeDiacritics(p.name.toLowerCase()).includes(removeDiacritics(toSearch.toLowerCase()))
    ),
    [places, toSearch]
  );

  // Tự động xác nhận chỉ khi có thay đổi địa điểm (không phải lần đầu mở modal)
  React.useEffect(() => {
    if (onlySelectTo) {
      // Nếu chỉ chọn điểm đến và đã chọn xong
      if (selectedTo && visible && hasConfirmed) {
        const timer = setTimeout(() => {
          onConfirm(initialFromId as number, selectedTo);
        }, 300);
        return () => clearTimeout(timer);
      }
    } else {
      // Nếu chọn cả điểm đi và điểm đến
      if (selectedFrom && selectedTo && visible && hasConfirmed) {
        const timer = setTimeout(() => {
          onConfirm(selectedFrom, selectedTo);
        }, 300);
        return () => clearTimeout(timer);
      }
    }
  }, [selectedFrom, selectedTo, visible, hasConfirmed, onConfirm, onlySelectTo, initialFromId]);

  // Hàm wrapper để đánh dấu đã confirm khi chọn địa điểm
  const handleSelectFrom = (id: number) => {
    setSelectedFrom(id);
    if (!hasConfirmed) {
      // Lần đầu chọn - tự động confirm ngay
      setTimeout(() => {
        if (onlySelectTo) {
          // Không áp dụng cho onlySelectTo
        } else {
          // Cần chọn cả điểm đến nữa
        }
      }, 100);
    } else {
      // Đã confirm trước đó - đánh dấu để tự động confirm khi đủ điều kiện
    }
    setHasConfirmed(true);
  };

  const handleSelectTo = (id: number) => {
    setSelectedTo(id);
    if (!hasConfirmed) {
      // Lần đầu chọn - tự động confirm ngay
      setTimeout(() => {
        if (onlySelectTo) {
          onConfirm(initialFromId as number, id);
        } else {
          // Cần chọn cả điểm đi nữa
          if (selectedFrom) {
            onConfirm(selectedFrom, id);
          }
        }
      }, 100);
    } else {
      // Đã confirm trước đó - đánh dấu để tự động confirm khi đủ điều kiện
    }
    setHasConfirmed(true);
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.centeredView}
        activeOpacity={1}
        onPress={onClose}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={e => e.stopPropagation()}
          style={styles.modalView}
        >
          <View style={styles.header}>
            <Text style={styles.title}>{t('placeModal.title')}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeBtn}>
              <MaterialIcons name="close" size={30} color="#2D5BFF" />
            </TouchableOpacity>
          </View>
          <View style={styles.columnsContainer}>
            {/* Cột chọn điểm đi */}
            {!onlySelectTo && (
              <View style={styles.column}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8, minHeight: 32 }}>
                  <Text style={styles.columnTitle}>{t('placeModal.from')}</Text>
                  {selectedFrom !== undefined && (
                    <View style={styles.selectedBadge}>
                      <Text style={styles.selectedBadgeText} numberOfLines={1}>{places.find(p => p.id === selectedFrom)?.name}</Text>
                      <TouchableOpacity onPress={() => setSelectedFrom(undefined)} style={styles.badgeCloseBtn}>
                        <MaterialIcons name="close" size={16} color="#888" />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                <View style={[styles.searchBox, fromInputFocused && styles.searchBoxFocused]}>
                  <MaterialIcons name="search" size={24} color={fromInputFocused ? '#2D5BFF' : '#888'} />
                  <TextInput
                    style={[styles.input, { outline: 'none', boxShadow: 'none' }]}
                    placeholder={t('placeModal.searchFrom')}
                    value={fromSearch}
                    onChangeText={setFromSearch}
                    onFocus={() => setFromInputFocused(true)}
                    onBlur={() => setFromInputFocused(false)}
                    placeholderTextColor="#A0A0A0"
                  />
                </View>
                <FlatList
                  data={filteredFrom}
                  keyExtractor={item => item.id.toString()}
                  style={[styles.list, { maxHeight: 320 }]}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={[styles.item, selectedFrom === item.id && styles.selectedItem]}
                      onPress={() => handleSelectFrom(item.id)}
                      activeOpacity={0.85}
                    >
                      <MaterialIcons name="location-on" size={26} color={selectedFrom === item.id ? '#2D5BFF' : '#888'} />
                      <Text style={[styles.itemText, selectedFrom === item.id && styles.selectedItemText]}>{item.name}</Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            )}
            {/* Cột chọn điểm đến */}
            <View style={styles.column}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8, minHeight: 32 }}>
                <Text style={styles.columnTitle}>{t('placeModal.to')}</Text>
                {selectedTo !== undefined && (
                  <View style={styles.selectedBadge}>
                    <Text style={styles.selectedBadgeText} numberOfLines={1}>{places.find(p => p.id === selectedTo)?.name}</Text>
                    <TouchableOpacity onPress={() => setSelectedTo(undefined)} style={styles.badgeCloseBtn}>
                      <MaterialIcons name="close" size={16} color="#888" />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
              <View style={[styles.searchBox, toInputFocused && styles.searchBoxFocused]}>
                <MaterialIcons name="search" size={24} color={toInputFocused ? '#2D5BFF' : '#888'} />
                <TextInput
                  style={[styles.input, { outline: 'none', boxShadow: 'none' }]}
                  placeholder={t('placeModal.searchTo')}
                  value={toSearch}
                  onChangeText={setToSearch}
                  onFocus={() => setToInputFocused(true)}
                  onBlur={() => setToInputFocused(false)}
                  placeholderTextColor="#A0A0A0"
                />
              </View>
              <FlatList
                data={filteredTo}
                keyExtractor={item => item.id.toString()}
                style={[styles.list, { maxHeight: 320 }]}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[styles.item, selectedTo === item.id && styles.selectedItem]}
                    onPress={() => handleSelectTo(item.id)}
                    activeOpacity={0.85}
                  >
                    <MaterialIcons name="location-on" size={26} color={selectedTo === item.id ? '#FF6B6B' : '#888'} />
                    <Text style={[styles.itemText, selectedTo === item.id && styles.selectedItemText]}>{item.name}</Text>
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
          <View style={{ height: 32 }} />
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.35)',
  },
  modalView: {
    width: width > 700 ? 650 : '95%',
    maxHeight: 600,
    backgroundColor: '#fff', // revert to white background
    borderRadius: 28,
    padding: 0,
    overflow: 'hidden',
    paddingBottom: 48,
    shadowColor: '#2D5BFF44',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 28,
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    position: 'relative',
    backgroundColor: '#fff', // ensure header is white
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2D5BFF', 
    flex: 1,
    textAlign: 'center',
  },
  closeBtn: {
    position: 'absolute',
    right: 18,
    top: 12,
    padding: 6,
    borderRadius: 22,
    backgroundColor: '#E9F1FF',
    zIndex: 2,
  },
  columnsContainer: {
    flexDirection: 'row',
    padding: 18,
    gap: 18,
    backgroundColor: 'transparent',
    borderRadius: 24,
  },
  column: {
    flex: 1,
  },
  columnTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#222', // dark text
    marginBottom: 0,
    lineHeight: 28,
    minHeight: 28,
    alignSelf: 'center',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F7FA', // light gray
    borderRadius: 16,
    paddingHorizontal: 16,
    marginBottom: 12,
    height: 48,
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#2D5BFF11',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
  },
  searchBoxFocused: {
    borderColor: '#2D5BFF',
    backgroundColor: '#E9F1FF',
  },
  input: {
    flex: 1,
    fontSize: 17,
    marginLeft: 10,
    color: '#222', // dark text
    fontWeight: '500',
    borderWidth: 0,
    backgroundColor: 'transparent',
  },
  list: {
    maxHeight: 260,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderRadius: 14,
    marginBottom: 8,
    backgroundColor: '#F5F7FA', // light gray
    shadowColor: '#2D5BFF11',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedItem: {
    backgroundColor: '#E9F1FF', // light blue
    borderColor: '#2D5BFF',
    shadowOpacity: 0.15,
  },
  itemText: {
    fontSize: 16,
    marginLeft: 8,
    color: '#222', // dark text
  },
  selectedItemText: {
    color: '#2D5BFF', // blue for selected
    fontWeight: 'bold',
  },

  selectedLabel: {
    marginLeft: 10,
    color: '#2D5BFF',
    fontWeight: 'bold',
    fontSize: 16,
    maxWidth: 120,
    flexShrink: 1,
  },
  selectedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E9F1FF', // light blue
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginLeft: 10,
    maxWidth: 140,
    minHeight: 28,
    marginTop: 0,
  },
  selectedBadgeText: {
    color: '#2D5BFF', // blue text
    fontWeight: 'bold',
    fontSize: 15,
    maxWidth: 100,
    flexShrink: 1,
    marginRight: 4,
  },
  badgeCloseBtn: {
    padding: 2,
    borderRadius: 10,
    marginLeft: 2,
  },
  autoConfirmContainer: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F8FAFF',
    borderRadius: 12,
    marginHorizontal: 18,
    marginBottom: 16,
  },
  autoConfirmText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmNowButton: {
    backgroundColor: '#2D5BFF',
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  confirmNowButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  confirmBtn: {
    borderRadius: 24,
    paddingVertical: 18,
    paddingHorizontal: 0,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    width: '100%',
    shadowColor: '#2D5BFF44',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    backgroundColor: '#2D5BFF',
  },
  confirmText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 20,
    letterSpacing: 1,
    textTransform: 'uppercase',
    textShadowColor: '#2D5BFF33',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default PlaceSelectionModal; 