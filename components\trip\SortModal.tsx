import { MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, Dimensions, Easing, Modal, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { Surface, Text } from 'react-native-paper';

export type SortOption = 'priceDesc' | 'priceAsc' | 'departureEarliest' | 'departureLatest';

interface SortModalProps {
  visible: boolean;
  onClose: () => void;
  selectedOption: SortOption;
  onSelectOption: (option: SortOption) => void;
}

const { height } = Dimensions.get('window');

const SortModal = ({ visible, onClose, selectedOption, onSelectOption }: SortModalProps) => {
  const { t } = useTranslation();
  const slideAnim = useRef(new Animated.Value(height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const sortOptions: { key: SortOption; label: string }[] = [
    { key: 'priceDesc', label: t('tripScreen.priceDesc') },
    { key: 'priceAsc', label: t('tripScreen.priceAsc') },
    { key: 'departureEarliest', label: t('tripScreen.departureEarliest') },
    { key: 'departureLatest', label: t('tripScreen.departureLatest') },
  ];

  useEffect(() => {
    if (visible) {
      // Hiển thị overlay trước
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }).start();
      
      // Sau đó trượt modal lên
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }).start();
    } else {
      // Trượt modal xuống trước
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 400,
        useNativeDriver: true,
        easing: Easing.in(Easing.cubic),
      }).start();
      
      // Sau đó ẩn overlay
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.in(Easing.ease),
      }).start();
    }
  }, [visible, slideAnim, fadeAnim]);

  const handleSelectOption = (option: SortOption) => {
    // Áp dụng lựa chọn ngay lập tức, nhưng không đóng modal
    onSelectOption(option);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        {/* Overlay với animation riêng - sử dụng TouchableWithoutFeedback thay vì Animated.View với onTouchEnd */}
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View 
            style={[
              styles.overlay,
              {
                opacity: fadeAnim,
              }
            ]}
          />
        </TouchableWithoutFeedback>
        
        {/* Content với animation riêng */}
        <Animated.View
          style={[
            styles.contentContainer,
            {
              transform: [{ translateY: slideAnim }],
            }
          ]}
        >
          <Surface style={styles.container} elevation={5}>
            <View style={styles.handleContainer}>
              <View style={styles.handle} />
            </View>
            <Text style={styles.title}>{t('tripScreen.sort')}</Text>
            
            {sortOptions.map((option) => (
              <TouchableOpacity 
                key={option.key}
                style={styles.option}
                onPress={() => handleSelectOption(option.key)}
              >
                <Text style={styles.optionText}>{option.label}</Text>
                {selectedOption === option.key && (
                  <MaterialIcons name="radio-button-checked" size={24} color="#2D5BFF" />
                )}
                {selectedOption !== option.key && (
                  <MaterialIcons name="radio-button-unchecked" size={24} color="#CCCCCC" />
                )}
              </TouchableOpacity>
            ))}
          </Surface>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  contentContainer: {
    width: '100%',
  },
  container: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  handleContainer: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 12,
    paddingBottom: 8,
  },
  handle: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#E0E0E0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
});

export default SortModal;



