import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Dimensions, FlatList, Image, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Surface } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { searchBusSchedules } from '../../api/busScheduleService';
import { getAllPlaces } from '../../api/placeService';
import DateTabs from '../../components/trip/DateTabs';
import EmptyState from '../../components/trip/EmptyState';
import FilterModal, { FilterOptions } from '../../components/trip/FilterModal';
import PickupDropoffModal from '../../components/trip/PickupDropoffModal';
import PlaceSelectionModal from '../../components/trip/PlaceSelectionModal';
import { SortOption } from '../../components/trip/SortModal';
import TripInfoModal from '../../components/trip/TripInfoModal';
import TripSelectionModal from '../../components/trip/TripSelectionModal';

const { width } = Dimensions.get('window');
const isMobile = width < 768;

// Define Trip type
type Trip = {
  id: string;
  departureTime: string;
  departureStation: string;
  arrivalTime: string;
  arrivalStation: string;
  duration: string;
  price: number;
  vehicleType: string;
  features: string[];
  vehicleImage: any;
};

// Mẫu dữ liệu cho các chuyến xe
const trips: Trip[] = [
  {
    id: '1',
    departureTime: '07:00',
    departureStation: 'Bến xe Đà Nẵng',
    arrivalTime: '19:00',
    arrivalStation: 'Bến xe Miền Đông',
    duration: '12h',
    price: 450000,
    vehicleType: 'Giường nằm',
    features: ['WiFi', 'Nước'],
    vehicleImage: require('../../assets/images/bus.jpg')
  },
  {
    id: '2',
    departureTime: '09:30',
    departureStation: 'Bến xe Đà Nẵng',
    arrivalTime: '21:30',
    arrivalStation: 'Bến xe Miền Đông',
    duration: '12h',
    price: 380000,
    vehicleType: 'Ghế ngồi',
    features: ['WiFi', 'Nước', 'Snack'],
    vehicleImage: require('../../assets/images/bus.jpg')
  },
  {
    id: '3',
    departureTime: '09:30',
    departureStation: 'Bến xe Đà Nẵng',
    arrivalTime: '21:30',
    arrivalStation: 'Bến xe Miền Đông',
    duration: '12h',
    price: 380000,
    vehicleType: 'Ghế ngồi',
    features: ['WiFi', 'Nước', 'Snack'],
    vehicleImage: require('../../assets/images/bus.jpg')
  },
  {
    id: '5',
    departureTime: '09:30',
    departureStation: 'Bến xe Đà Nẵng',
    arrivalTime: '21:30',
    arrivalStation: 'Bến xe Miền Đông',
    duration: '12h',
    price: 380000,
    vehicleType: 'Ghế ngồi',
    features: ['WiFi', 'Nước', 'Snack'],
    vehicleImage: require('../../assets/images/bus.jpg')
  },
  // Thêm các chuyến xe khác nếu cần
];

interface Place {
  id: number;
  name: string;
}

export default function TripsScreen() {
  const { t, i18n } = useTranslation();
  const params = useLocalSearchParams();
  // const fromPlaceId = Array.isArray(params.fromPlaceId) ? params.fromPlaceId[0] : params.fromPlaceId;
  // const fromPlaceName = Array.isArray(params.fromPlaceName) ? params.fromPlaceName[0] : params.fromPlaceName;
  // const toPlaceId = Array.isArray(params.toPlaceId) ? params.toPlaceId[0] : params.toPlaceId;
  // const toPlaceName = Array.isArray(params.toPlaceName) ? params.toPlaceName[0] : params.toPlaceName;

  // State filter nội bộ
  const [places, setPlaces] = useState<Place[]>([]);
  const [fromPlaceId, setFromPlaceId] = useState<string | undefined>(undefined);
  const [fromPlaceName, setFromPlaceName] = useState<string | undefined>(undefined);
  const [toPlaceId, setToPlaceId] = useState<string | undefined>(undefined);
  const [toPlaceName, setToPlaceName] = useState<string | undefined>(undefined);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [tripModalVisible, setTripModalVisible] = useState(false);
  const [searchTrips, setSearchTrips] = useState<any[]>([]);
  const [loadingTrips, setLoadingTrips] = useState(false);
  const [sortOption, setSortOption] = useState<SortOption>('priceDesc'); // Default sort option
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    departureTimeSlots: [],
    priceRange: [100000, 1000000],
    busTypes: [],
    sortOption: 'priceDesc',
  });

  // Thêm state cho modal chọn điểm
  const [showFromModal, setShowFromModal] = useState(false);
  const [showToModal, setShowToModal] = useState(false);
  const [showPlaceModal, setShowPlaceModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Lấy danh sách địa điểm khi vào màn hình
  useEffect(() => {
    getAllPlaces().then(res => {
      setPlaces(res.data);
      // Nếu chưa có fromPlaceId thì mặc định là Hà Nội
      if (!fromPlaceId) {
        const haNoi = res.data.find((p: Place) => p.name.toLowerCase().includes('hà nội'));
        if (haNoi) {
          setFromPlaceId(String(haNoi.id));
          setFromPlaceName(haNoi.name);
        }
      }
    });
  }, []);

  // Hiện modal chọn điểm đến khi vào trang
  useEffect(() => {
    setShowPlaceModal(true);
  }, []);

  // Khi chọn điểm đi
  const handleSelectFromPlace = (placeId: string) => {
    const place = places.find(p => p.id === Number(placeId));
    if (place) {
      setFromPlaceId(String(place.id));
      setFromPlaceName(place.name);
    }
  };
  // Khi chọn điểm đến
  const handleSelectToPlace = (placeId: string) => {
    const place = places.find(p => p.id === Number(placeId));
    if (place) {
      setToPlaceId(String(place.id));
      setToPlaceName(place.name);
    }
  };
  // Khi xác nhận chọn điểm đi/đến từ modal
  const handleConfirmPlaces = (fromId: number, toId: number) => {
    const fromPlace = places.find(p => p.id === fromId);
    const toPlace = places.find(p => p.id === toId);
    if (fromPlace && toPlace) {
      setFromPlaceId(String(fromPlace.id));
      setFromPlaceName(fromPlace.name);
      setToPlaceId(String(toPlace.id));
      setToPlaceName(toPlace.name);
      setShowPlaceModal(false);
    }
  };

  // Gọi API tìm kiếm chuyến xe khi filter thay đổi
  useEffect(() => {
    if (fromPlaceId && toPlaceId) {
      setLoadingTrips(true);
      const departureDate = format(selectedDate, 'yyyy-MM-dd');
      searchBusSchedules({
        fromPlaceId,
        toPlaceId,
        departureDate,
        numTickets: 1,
        fromPlaceName,
        toPlaceName,
      })
        .then(res => {
          console.log(res.data); 
          setSearchTrips(res.data);
          setLoadingTrips(false);
        })
        .catch(() => setLoadingTrips(false));
    }
  }, [fromPlaceId, toPlaceId, fromPlaceName, toPlaceName, selectedDate, filterOptions]);

  // Lọc chuyến xe theo filterOptions mới
  const filteredTrips = React.useMemo(() => {
    return searchTrips.filter(item => {
      // Lọc giá vé
      const price = item.price ?? item.dropoffs?.[0]?.ticket_price ?? 0;
      if (price < filterOptions.priceRange[0] || price > filterOptions.priceRange[1]) return false;
      // Lọc loại xe
      if (filterOptions.busTypes.length > 0) {
        const busTypeRaw = (item.bus?.bus_type || item.bus?.model || '').toLowerCase();
        let normalizedType = '';
        if (busTypeRaw.includes('bed') || busTypeRaw.includes('giuong')) {
          normalizedType = 'bed';
        } else if (busTypeRaw.includes('chair') || busTypeRaw.includes('ghe')) {
          normalizedType = 'chair';
        } else if (busTypeRaw.includes('limousine')) {
          normalizedType = 'limousine';
        }
        if (!filterOptions.busTypes.includes(normalizedType)) return false;
      }
      // Lọc khung giờ đi
      if (filterOptions.departureTimeSlots.length > 0) {
        const depTime = item.departure_time || item.departureTime;
        if (!depTime) return false;
        const [h, m] = depTime.split(':').map(Number);
        const time = h + (m || 0) / 60;
        const slotMap = {
          early: [0, 6],
          morning: [6, 12],
          afternoon: [12, 18],
          evening: [18, 24],
        };
        let inSlot = false;
        for (const slot of filterOptions.departureTimeSlots) {
          const [from, to] = slotMap[slot as keyof typeof slotMap] || [0, 24];
          if (time >= from && time < to) inSlot = true;
        }
        if (!inSlot) return false;
      }
      return true;
    });
  }, [searchTrips, filterOptions]);

  // Sắp xếp danh sách chuyến xe theo sortOption trong filterOptions
  const sortedTrips = React.useMemo(() => {
    if (!filteredTrips || filteredTrips.length === 0) return [];
    if (!filterOptions.sortOption) return filteredTrips; // Không sort, giữ nguyên thứ tự API
    const sorted = [...filteredTrips];
    switch (filterOptions.sortOption) {
      case 'priceAsc':
        return sorted.sort((a, b) => {
          const priceA = a.price ?? a.dropoffs?.[0]?.ticket_price ?? 0;
          const priceB = b.price ?? b.dropoffs?.[0]?.ticket_price ?? 0;
          return priceA - priceB;
        });
      case 'priceDesc':
        return sorted.sort((a, b) => {
          const priceA = a.price ?? a.dropoffs?.[0]?.ticket_price ?? 0;
          const priceB = b.price ?? b.dropoffs?.[0]?.ticket_price ?? 0;
          return priceB - priceA;
        });
      case 'departureEarliest':
        return sorted.sort((a, b) => {
          const tA = a.departure_time || a.departureTime;
          const tB = b.departure_time || b.departureTime;
          return (tA || '').localeCompare(tB || '');
        });
      case 'departureLatest':
        return sorted.sort((a, b) => {
          const tA = a.departure_time || a.departureTime;
          const tB = b.departure_time || b.departureTime;
          return (tB || '').localeCompare(tA || '');
        });
      default:
        return sorted;
    }
  }, [filteredTrips, filterOptions.sortOption]);

  // Render card chuyến xe
  const renderTripItem = ({ item }: { item: any }) => {
    // Lấy dữ liệu thực tế từ API
    const busImage = item.bus?.images?.[0]?.sizes?.medium || require('../../assets/images/bus.jpg');
    const fleetName = item.bus?.fleet?.name || '';
    const busTypeRaw = item.bus?.bus_type || item.bus?.model || '';
    let busType = '';
    if (busTypeRaw.toLowerCase().includes('bed') || busTypeRaw.toLowerCase().includes('giuong')) {
      busType = t('vehicleType.bed');
    } else if (busTypeRaw.toLowerCase().includes('chair') || busTypeRaw.toLowerCase().includes('ghe')) {
      busType = t('vehicleType.chair');
    } else if (busTypeRaw.toLowerCase().includes('limousine')) {
      busType = t('vehicleType.limousine');
    } else {
      busType = busTypeRaw;
    }
    const seats = item.bus?.seats ?? item.bus?.total_seats ?? '';
    const availableSeats = item.available_seats ?? item.remainingTickets ?? '';
    const price = item.price ?? (item.dropoffs?.[0]?.ticket_price ?? null);
    const departureName = item.departure_place?.name || '';
    const arrivalName = item.dropoffs?.[0]?.arrival_place?.name || item.dropoffs?.[0]?.location_name || '';
    const departureTime = item.departure_time?.slice(0, 5) || '';
    const arrivalTime = item.arrival_time?.slice(0, 5) || '';
    // Tính thời lượng chuyến
    let duration = '';
    if (item.departure_time && item.arrival_time) {
      const [h1, m1] = item.departure_time.split(':').map(Number);
      const [h2, m2] = item.arrival_time.split(':').map(Number);
      let mins = (h2 * 60 + m2) - (h1 * 60 + m1);
      if (mins < 0) mins += 24 * 60;
      duration = `${Math.floor(mins / 60)}h${mins % 60 ? ` ${mins % 60}m` : ''}`;
    }
    // Ngày (ví dụ lấy ngày hiện tại)
    const today = new Date();
    const dateStr = `(${String(today.getDate()).padStart(2, '0')}-${String(today.getMonth() + 1).padStart(2, '0')})`;

    // --- Dưới đây GIỮ NGUYÊN layout và 3 nút như cũ ---
    return (
      <Surface style={styles.tripCard} elevation={4}>
        <View style={styles.tripHeader}>
          <View style={styles.timeContainer}>
            <Text style={styles.timeText}>{departureTime}</Text>
            <Text style={styles.stationText}>{departureName}</Text>
          </View>
          <View style={styles.durationContainer}>
            <Text style={styles.durationText}>{duration}</Text>
            <View style={styles.durationLine}>
              <View style={styles.durationDot} />
              <View style={styles.durationBar} />
              <View style={styles.durationDot} />
            </View>
          </View>
          <View style={styles.arrivalContainer}>
            <Text style={styles.timeText}>{arrivalTime}</Text>
            <Text style={styles.stationText}>{arrivalName}</Text>
          </View>
        </View>
        {/* Thông tin xe cho mobile: đặt ngay dưới thời gian di chuyển, trên các nút */}
        {isMobile && (
          <View
            style={{
              marginTop: 8,
              marginBottom: 8,
              backgroundColor: '#F5F7FF',
              borderRadius: 14,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <View style={{ flex: 1, flexDirection: 'row', flexWrap: 'wrap' }}>
              <View style={{ flex: 1, minWidth: 120 }}>
                <Text style={{ fontSize: 14, color: '#222', fontWeight: '600', marginBottom: 2 }}>
                  {t('tripScreen.priceLabel')}: <Text style={{ color: '#2D5BFF', fontWeight: 'bold' }}>{price != null ? price.toLocaleString() + ' đ' : t('tripScreen.contactForPrice')}</Text>
                </Text>
                <Text style={{ fontSize: 13, color: '#444', marginBottom: 2 }}>
                  {t('tripScreen.busName')}: <Text style={{ color: '#222', fontWeight: '500' }}>{item.bus?.name || fleetName || t('tripScreen.unknown')}</Text>
                </Text>
              </View>
              <View style={{ flex: 1, minWidth: 120 }}>
                <Text style={{ fontSize: 13, color: '#444', marginBottom: 2 }}>
                  {t('tripScreen.busType')}: <Text style={{ color: '#222', fontWeight: '500' }}>{busType || t('tripScreen.unknown')}</Text>
                </Text>
                <Text style={{ fontSize: 13, color: '#444' }}>
                  {t('tripScreen.availableSeats')}: <Text style={{ color: '#2D5BFF', fontWeight: 'bold' }}>{availableSeats || t('tripScreen.unknown')}</Text>
                </Text>
              </View>
            </View>
            <Image
              source={typeof busImage === 'string' ? { uri: busImage } : busImage}
              style={{ width: 70, height: 48, borderRadius: 10, marginLeft: 16, backgroundColor: '#e9e9e9' }}
              resizeMode="cover"
            />
          </View>
        )}
        <View style={styles.tripDetails}>
          {/* Thông tin xe cho PC - ẩn trên mobile */}
          {!isMobile && (
            <View style={styles.vehicleInfoContainer}>
              <Surface style={styles.vehicleImageContainer} elevation={2}>
                <Image
                  source={typeof busImage === 'string' ? { uri: busImage } : busImage}
                  style={styles.vehicleImage}
                  resizeMode="cover"
                />
              </Surface>
              <View style={styles.priceAndFeatureContainer}>
                <Text style={styles.priceText}>
                  {price != null ? price.toLocaleString() + ' đ' : t('tripScreen.contactForPrice')}
                </Text>
                <View style={styles.featureContainer}>
                  <MaterialIcons name="airline-seat-recline-extra" size={14} color="#666" />
                  <Text style={styles.featureText}>
                    {fleetName} • {busType} • {seats} {t('tripScreen.emptySeats')}
                  </Text>
                </View>
              </View>
            </View>
          )}
          {/* Các nút */}
          {isMobile ? (
            <View style={{ width: '100%', marginTop: 10 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 10 }}>
                <TouchableOpacity style={[styles.mobileActionButton, { marginRight: 8 }]} onPress={() => { setSelectedTripId(item.id); setShowScheduleModal(true); }}>
                  <MaterialIcons name="schedule" size={18} color="#2D5BFF" style={{ marginRight: 6 }} />
                  <Text style={styles.mobileActionButtonText}>{t('tripScreen.schedule')}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.mobileActionButton, { marginLeft: 8 }]} onPress={() => { setSelectedTripId(item.id); setShowPolicyModal(true); }}>
                  <MaterialIcons name="info-outline" size={18} color="#2D5BFF" style={{ marginRight: 6 }} />
                  <Text style={styles.mobileActionButtonText}>{t('tripScreen.policy')}</Text>
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                style={styles.mobileBookButton}
                onPress={() => router.push({
                  pathname: '/passenger-info',
                  params: {
                    trip: JSON.stringify(item),
                    numTickets: item.available_seats ?? item.remainingTickets ?? 1,
                    date: selectedDate.toISOString(),
                  }
                })}
              >
                <LinearGradient
                  colors={['#2D5BFF', '#4A90E2']}
                  style={styles.mobileBookButtonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.mobileBookButtonText}>{t('tripScreen.bookNow')}</Text>
                  <MaterialIcons name="arrow-forward" size={20} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.infoButtonsContainer}>
              <TouchableOpacity style={styles.infoButton} onPress={() => { setSelectedTripId(item.id); setShowScheduleModal(true); }}>
                <MaterialIcons name="schedule" size={16} color="#2D5BFF" />
                <Text style={styles.infoButtonText}>{t('tripScreen.schedule')}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.infoButton} onPress={() => { setSelectedTripId(item.id); setShowPolicyModal(true); }}>
                <MaterialIcons name="info-outline" size={16} color="#2D5BFF" />
                <Text style={styles.infoButtonText}>{t('tripScreen.policy')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.bookButton}
                onPress={() => router.push({
                  pathname: '/passenger-info',
                  params: {
                    trip: JSON.stringify(item),
                    numTickets: item.available_seats ?? item.remainingTickets ?? 1,
                    date: selectedDate.toISOString(),
                  }
                })}
              >
                <LinearGradient
                  colors={['#2D5BFF', '#4A90E2']}
                  style={styles.bookButtonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.bookButtonText}>{t('tripScreen.bookNow')}</Text>
                  <MaterialIcons name="arrow-forward" size={18} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Surface>
    );
  };

  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showPolicyModal, setShowPolicyModal] = useState(false);
  const [selectedTripId, setSelectedTripId] = useState<string | undefined>(undefined);
  const [showFilterModal, setShowFilterModal] = useState(false);

  const handleApplyFilters = (filters: FilterOptions) => {
    setFilterOptions(filters);
    // Thêm logic lọc trips ở đây
    // Khi filter thay đổi, useEffect sẽ tự động gọi lại API với filter mới
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <StatusBar barStyle="light-content" backgroundColor="#2D5BFF" />
      <LinearGradient
        colors={['#2D5BFF', '#4A90E2', '#87CEEB', '#F0F8FF']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.container}>
          {/* Premium Header with Logo */}
          <Surface style={styles.headerSurface} elevation={4}>
            <View style={styles.header}>
              <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                <Ionicons name="arrow-back" size={28} color="#fff" />
              </TouchableOpacity>
              <View style={styles.headerTitleContainer}>
                <View style={styles.routeAndDateRow}>
                  <TouchableOpacity
                    style={[
                      styles.placeMainButton,
                      isMobile && {
                        paddingVertical: 6,
                        paddingHorizontal: 10,
                        borderRadius: 12,
                        minWidth: 0,
                        marginRight: 0,
                        shadowRadius: 2,
                      }
                    ]}
                    onPress={() => setShowPlaceModal(true)}
                  >
                    <View style={styles.swapIconCircle}>
                      <MaterialIcons name="swap-vert" size={18} color="#2D5BFF" />
                    </View>
                    <Text style={[
                      styles.placeMainButtonText,
                      isMobile && { fontSize: 15, letterSpacing: 0.2, textAlign: 'center', flexDirection: 'column' }
                    ]}>
                      {isMobile ? (
                        <>
                          <Text style={[styles.placeFromText, { textAlign: 'center' }]}>
                            {fromPlaceName ? fromPlaceName : t('tripScreen.selectFrom')}
                          </Text>{"\n"}
                          <Text style={[styles.arrowText, { textAlign: 'center' }]}>→ </Text>
                          <Text style={[styles.placeToText, { textAlign: 'center' }]}>
                            {toPlaceName ? toPlaceName : t('tripScreen.selectTo')}
                          </Text>
                        </>
                      ) : (
                        <>
                          {fromPlaceName ? (
                            <Text style={styles.placeFromText}>{fromPlaceName}</Text>
                          ) : t('tripScreen.selectFrom')}
                          <Text style={styles.arrowText}> → </Text>
                          {toPlaceName ? (
                            <Text style={styles.placeToText}>{toPlaceName}</Text>
                          ) : t('tripScreen.selectTo')}
                        </>
                      )}
                    </Text>
                  </TouchableOpacity>
                  {/* Date display as non-interactive text */}
                  {!isMobile && (
                    <View style={styles.dateRow}>
                      <MaterialIcons name="event" size={18} color="#2D5BFF" style={{ marginRight: 6 }} />
                      <Text style={styles.dateText}>
                        {format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: i18n.language === 'vi' ? vi : enUS })}
                      </Text>
                    </View>)}
                </View>
              </View>
              {/* Icon filter mới ở header */}
              <TouchableOpacity onPress={() => setShowFilterModal(true)} style={styles.filterButton}>
                <Ionicons name="filter" size={28} color="#fff" />
              </TouchableOpacity>
            </View>
          </Surface>

          {/* Premium Date Selector */}
          <Surface style={styles.dateTabsContainer} elevation={2}>
            <DateTabs
              onSelectDate={handleDateSelect}
              selectedDate={selectedDate}
            />
          </Surface>

          {/* Main Content */}
          <View style={styles.contentContainer}>
            {loadingTrips ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#2D5BFF" />
                <Text style={styles.loadingText}>{t('tripScreen.loading')}</Text>
              </View>
            ) : (
              <>
                {(!fromPlaceId || !toPlaceId) ? (
                  <View style={styles.emptyStateContainer}>
                    <EmptyState
                      title={t('emptyState.selectToTitle')}
                      subtitle={t('emptyState.selectToSubtitle')}
                    />
                  </View>
                ) : (
                  <>
                    {sortedTrips.length === 0 ? (
                      <View style={styles.emptyStateContainer}>
                        <EmptyState
                          title={t('emptyState.notFoundTitle')}
                          subtitle={t('emptyState.notFoundSubtitle')}
                        />
                      </View>
                    ) : (
                      <FlatList
                        data={sortedTrips}
                        renderItem={renderTripItem}
                        keyExtractor={item => item.id}
                        contentContainerStyle={styles.listContainer}
                        showsVerticalScrollIndicator={false}
                      />
                    )}
                  </>
                )}
              </>
            )}
          </View>

          {/* XÓA PHẦN FOOTER filter/sort ở đây */}
        </View>
      </LinearGradient>
      <TripInfoModal
        visible={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        type="schedule"
        tripId={selectedTripId}
      />
      <TripInfoModal
        visible={showPolicyModal}
        onClose={() => setShowPolicyModal(false)}
        type="policy"
        tripId={selectedTripId}
      />
      {/* XÓA SortModal cũ khỏi render */}
      {/* <SortModal
        visible={showSortModal}
        onClose={() => setShowSortModal(false)}
        selectedOption={sortOption}
        onSelectOption={setSortOption}
      /> */}
      <FilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApplyFilters={handleApplyFilters}
      />
      <TripSelectionModal
        visible={tripModalVisible}
        onClose={() => setTripModalVisible(false)}
        onSearch={() => setTripModalVisible(false)}
      />
      {/* Modal chọn điểm đi */}
      <PickupDropoffModal
        visible={showFromModal}
        onClose={() => setShowFromModal(false)}
        type="pickup"
        locations={places.map(p => ({ id: String(p.id), name: p.name, address: '', time: '' }))}
        selectedLocationId={String(fromPlaceId || '')}
        onSelectLocation={handleSelectFromPlace}
      />
      {/* Modal chọn điểm đến */}
      <PickupDropoffModal
        visible={showToModal}
        onClose={() => setShowToModal(false)}
        type="dropoff"
        locations={places.map(p => ({ id: String(p.id), name: p.name, address: '', time: '' }))}
        selectedLocationId={String(toPlaceId || '')}
        onSelectLocation={handleSelectToPlace}
      />
      <PlaceSelectionModal
        visible={showPlaceModal}
        onClose={() => setShowPlaceModal(false)}
        places={places}
        initialFromId={fromPlaceId ? Number(fromPlaceId) : (places.find(p => p.name.toLowerCase().includes('hà nội'))?.id)}
        initialToId={toPlaceId ? Number(toPlaceId) : undefined}
        onConfirm={(fromId, toId) => handleConfirmPlaces(fromId, toId)}
        onlySelectTo // custom prop để chỉ chọn điểm đến
      />
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display="calendar"
          onChange={(_, date) => {
            setShowDatePicker(false);
            if (date) setSelectedDate(date);
          }}
          minimumDate={new Date()}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  headerSurface: {
    backgroundColor: 'rgba(45, 91, 255, 0.8)',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
    paddingHorizontal: 24,
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flex: 1,
    marginLeft: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginLeft: 4,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateTabsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    marginHorizontal: 24,
    marginBottom: 20,
  },
  dateTabsList: {
    paddingVertical: 8,
  },
  dateTab: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
    marginHorizontal: 4,
  },
  activeDateTab: {
    borderBottomColor: '#2D5BFF',
  },
  dateTabDay: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  dateTabDate: {
    fontSize: 18,
    color: '#333',
    fontWeight: '600',
    marginTop: 4,
  },
  activeDateTabText: {
    color: '#2D5BFF',
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
  },
  listContainer: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingBottom: 100,
  },
  tripCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    marginBottom: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeContainer: {
    flex: 2,
  },
  arrivalContainer: {
    alignItems: 'flex-end',
    flex: 2,
  },
  timeText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2D5BFF',
  },
  stationText: {
    fontSize: 15,
    color: '#555',
    flexWrap: 'wrap',
    marginTop: 6,
    maxWidth: width * 0.3,
  },
  durationContainer: {
    flex: 1,
    alignItems: 'center',
  },
  durationText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    fontWeight: '500',
  },
  durationLine: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  durationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#2D5BFF',
  },
  durationBar: {
    flex: 1,
    height: 2,
    backgroundColor: '#2D5BFF',
  },
  tripDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
    paddingTop: 20,
  },
  vehicleInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vehicleImageContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 16,
  },
  vehicleImage: {
    width: 70,
    height: 50,
    borderRadius: 8,
  },
  priceAndFeatureContainer: {
    flex: 1,
  },
  priceText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D5BFF',
  },
  featureContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  featureText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  bookButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 4,
  },
  bookButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  bookButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginRight: 8,
  },
  infoButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 15,
    marginBottom: 16,
  },
  infoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(45, 91, 255, 0.1)',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginRight: 16,
    marginTop: 4,
  },
  infoButtonText: {
    color: '#2D5BFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 4,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingVertical: 20,
    paddingHorizontal: 30,
  },
  footerButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    marginHorizontal: 10,
  },
  footerButtonGradient: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
  },
  footerButtonText: {
    marginLeft: 10,
    color: '#2D5BFF',
    fontWeight: '600',
    fontSize: 16,
  },
  // Thêm style cho nút chọn điểm đi/điểm đến
  placeSelectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginHorizontal: 2,
    minWidth: 160,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  placeSelectText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D5BFF',
    marginLeft: 8,
  },
  // Thêm style cho nút chọn điểm đi/điểm đến lớn
  routeAndDateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 18,
    marginBottom: 8,
    gap: 18,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.85)',
    borderRadius: 16,
    paddingVertical: 10,
    paddingHorizontal: 18,
    marginLeft: 18,
    shadowColor: '#2D5BFF11',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
  },
  dateText: {
    fontSize: 18,
    color: '#2D5BFF',
    fontWeight: 'bold',
  },
  placeMainButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.7)',
    borderRadius: 18,
    paddingVertical: 10,
    paddingHorizontal: 18,
    shadowColor: '#2D5BFF33',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    borderWidth: 1.2,
    borderColor: 'rgba(45,91,255,0.08)',
    marginRight: 18,
  },
  swapIconCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#E9F1FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  placeMainButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D5BFF',
    letterSpacing: 0.5,
  },
  placeFromText: {
    color: '#2D5BFF',
    fontWeight: 'bold',
    fontSize: 18,
  },
  arrowText: {
    color: '#888',
    fontWeight: 'normal',
    fontSize: 18,
  },
  placeToText: {
    color: 'rgb(255, 107, 107)',
    fontWeight: 'bold',
    fontSize: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    fontSize: 16,
    color: '#2D5BFF',
    fontWeight: '500',
    marginTop: 12,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    minHeight: 500,
  },
  mobileInfoButtonsContainer: {
    width: '100%',
    marginTop: 10,
  },
  mobileInfoButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  // Thêm style cho mobile action button và book button
  mobileActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F7FF',
    borderRadius: 14,
    paddingVertical: 12,
    paddingHorizontal: 0,
    minWidth: 0,
    shadowColor: '#2D5BFF11',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
  },
  mobileActionButtonText: {
    color: '#2D5BFF',
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 4,
  },
  mobileBookButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginTop: 0,
    width: '100%',
  },
  mobileBookButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 0,
    width: '100%',
  },
  mobileBookButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 17,
    marginRight: 8,
  },
});
