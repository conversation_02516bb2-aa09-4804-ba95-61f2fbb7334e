import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Dimensions,
    Image,
    Modal,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import { Surface } from 'react-native-paper';
import QRCode from 'react-native-qrcode-svg';
import QRCodeCard from '../../components/QRCodeCard';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
    gradient: {
        flex: 1,
    },
    container: {
        flex: 1,
        paddingTop: 40,
    },
    headerCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.15)',
        borderRadius: 24,
        marginBottom: 20,
        marginHorizontal: 16,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.2)',
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 24,
        paddingVertical: 20,
    },
    backButton: {
        width: 44,
        height: 44,
        borderRadius: 22,
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.3)',
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: '700',
        color: '#FFFFFF',
        textAlign: 'center',
        flex: 1,
        marginHorizontal: 16,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 3,
    },
    headerDecoration: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    decorationDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#FFFFFF',
        marginHorizontal: 2,
        opacity: 0.8,
    },
    decorationDotSecondary: {
        backgroundColor: '#FF6B6B',
    },
    decorationDotTertiary: {
        backgroundColor: '#4ECDC4',
    },
    scrollView: {
        flex: 1,
    },
    content: {
        flex: 1,
        backgroundColor: '#FFFFFF',
        borderRadius: 24,
        paddingHorizontal: 24,
        paddingVertical: 24,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
        marginBottom: 20,
        marginHorizontal: 16,
    },
    tripInfoHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20,
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    dateText: {
        marginLeft: 6,
        fontSize: 14,
        color: '#2D5BFF',
        fontWeight: '500',
    },
    durationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    durationText: {
        marginLeft: 6,
        fontSize: 14,
        color: '#2D5BFF',
        fontWeight: '500',
    },
    dashedLine: {
        height: 1,
        backgroundColor: 'transparent',
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor: '#d1d5db',
        marginVertical: 20,
    },
    routeSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 30,
    },
    locationContainer: {
        alignItems: 'center',
        flex: 1,
    },
    cityCode: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#000',
        marginBottom: 4,
    },
    cityName: {
        fontSize: 14,
        color: '#6b7280',
    },
    busContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        justifyContent: 'center',
    },
    busLine: {
        height: 1,
        backgroundColor: '#d1d5db',
        flex: 1,
    },
    busIcon: {
        backgroundColor: '#2D5BFF',
        borderRadius: 20,
        width: 40,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 10,
    },
    tripDetails: {
        marginBottom: 20,
    },
    detailRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    detailItem: {
        flex: 1,
    },
    detailLabel: {
        fontSize: 14,
        color: '#6b7280',
        marginBottom: 4,
    },
    detailValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#000',
    },
    qrContainer: {
        alignItems: 'center',
        marginVertical: 20,
    },
    qrCodeWrapper: {
        padding: 12,
        borderWidth: 2,
        borderColor: '#2D5BFF',
        borderRadius: 16,
        backgroundColor: '#fff',
        shadowColor: '#2D5BFF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    qrCode: {
        width: 180,
        height: 180,
    },
    tapToEnlargeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 8,
        backgroundColor: 'rgba(45, 91, 255, 0.1)',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
    },
    tapToEnlargeText: {
        fontSize: 12,
        color: '#2D5BFF',
        marginLeft: 4,
    },
    ticketId: {
        marginTop: 12,
        fontSize: 16,
        color: '#000',
        fontWeight: '500',
    },
    downloadButton: {
        backgroundColor: '#2D5BFF',
        borderRadius: 12,
        paddingVertical: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 20,
    },
    downloadIcon: {
        marginRight: 8,
    },
    downloadButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '80%',
        backgroundColor: '#fff',
        borderRadius: 20,
        overflow: 'hidden',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    closeButton: {
        padding: 5,
    },
    enlargedQRContainer: {
        alignItems: 'center',
        padding: 20,
    },
    enlargedQR: {
        width: 250,
        height: 250,
    },
    modalFooter: {
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    modalFooterText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
    },
    passengerInfo: {
        backgroundColor: '#f8faff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 20,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2D5BFF',
        marginLeft: 8,
    },
    infoRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#6b7280',
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#1f2937',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
});

// Logic xử lý dữ liệu động
const PrintTicketScreen = () => {
    const { t, i18n } = useTranslation();
    const params = useLocalSearchParams();
    const [qrModalVisible, setQrModalVisible] = useState(false);
    const [selectedQR, setSelectedQR] = useState<{ qr: string, ticket: string } | null>(null);

    // Lấy ngày từ params.date nếu có
    let selectedDateStr = '';
    if (params.date) {
      try {
        const d = new Date(params.date as string);
        selectedDateStr = format(d, 'EEEE, dd/MM/yyyy', { locale: i18n.language === 'vi' ? vi : enUS });
      } catch {}
    }

    // Thêm chức năng tự động quay về home sau 10s
    useEffect(() => {
      const timeout = setTimeout(() => {
        router.replace('/');
      }, 360000);
      return () => clearTimeout(timeout);
    }, []);

    // Parse data from params.printData
    let ticketData = null;
    let ticketsList: any[] = [];
    let tripData = null;
    try {
      if (params.printData) {
        const data = typeof params.printData === 'string' ? JSON.parse(params.printData) : params.printData;
        // Lấy mảng tickets nếu có
        ticketsList = Array.isArray(data.tickets) ? data.tickets : [];
        // Lấy tripData nếu có
        if (params.tripData) {
          try {
            tripData = typeof params.tripData === 'string' ? JSON.parse(params.tripData) : params.tripData;
          } catch (e) {
            tripData = null;
            console.error('Lỗi parse tripData:', e, params.tripData);
          }
        }
        // Lấy ticket đầu tiên để lấy thông tin chung
        const ticket = (ticketsList[0] && data.external_response_data?.tickets?.find((t: any) => t.ticket_number === ticketsList[0].ticket_number)) || data.external_response_data?.tickets?.[0] || data.tickets?.[0] || data;
        const orderTrip = ticket?.bus_schedule;
        const dropoff = ticket?.dropoff;
        // Sử dụng tripData nếu có, không thì fallback về order data
        const trip = tripData || orderTrip;
        const bus = trip?.bus;
        const fleet = bus?.fleet;
        const depPlace = trip?.departure_place;
        const tripDropoff = tripData?.dropoffs?.[0] || dropoff;
        const arrPlace = tripDropoff?.arrival_place;
        // Xử lý bus type đa ngôn ngữ
        const busTypeRaw = bus?.bus_type || bus?.model || data.busType || '';
        let busType = '';
        if (busTypeRaw.toLowerCase().includes('bed') || busTypeRaw.toLowerCase().includes('giuong')) {
          busType = t('vehicleType.bed');
        } else if (busTypeRaw.toLowerCase().includes('chair') || busTypeRaw.toLowerCase().includes('ghe')) {
          busType = t('vehicleType.chair');
        } else if (busTypeRaw.toLowerCase().includes('limousine')) {
          busType = t('vehicleType.limousine');
        } else {
          busType = busTypeRaw;
        }
        // Format ngày theo ngôn ngữ
        const formatDate = () => {
          const today = new Date();
          if (i18n.language === 'vi') {
            const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
            const dayName = dayNames[today.getDay()];
            return `${dayName}, ${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}`;
          } else {
            const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const dayName = dayNames[today.getDay()];
            const monthName = monthNames[today.getMonth()];
            return `${dayName}, ${monthName} ${today.getDate().toString().padStart(2, '0')}`;
          }
        };
        ticketData = {
          ticketId: ticket?.ticket_number || data.order_number || data.ticketId || '',
          date: ticket?.departure_date || data.date || formatDate(),
          departure: {
            time: trip?.departure_time?.slice(0, 5) || ticket?.departure_time || data.time || '',
            city: depPlace?.name || data.departure?.city || '',
          },
          arrival: {
            time: trip?.arrival_time?.slice(0, 5) || ticket?.arrival_time || data.arrival?.time || '',
            city: arrPlace?.name || tripDropoff?.location_name || data.arrival?.city || '',
          },
          duration: tripDropoff?.duration_minutes
            ? `${Math.floor(tripDropoff.duration_minutes/60)} ${i18n.language === 'vi' ? 'giờ' : 'hours'}${tripDropoff.duration_minutes%60 ? ' ' + (tripDropoff.duration_minutes%60) + (i18n.language === 'vi' ? ' phút' : ' mins') : ''}`
            : data.duration || '',
          busType: busType,
          fleetName: fleet?.name || bus?.brand || '',
          licensePlate: bus?.license_plate || '',
          seatNumber: ticket?.seat_number || ticket?.seat || '',
          price: tripDropoff?.ticket_price || ticket?.price || data.price || '',
          quantity: ticketsList.length > 0 ? ticketsList.length : (ticket?.quantity || data.quantity || 1),
          totalAmount: ticketsList.length > 0
            ? ticketsList.reduce((sum, tk) => sum + (tk.price || tripDropoff?.ticket_price || 0), 0)
            : (data.total_amount || (tripDropoff?.ticket_price * (ticket?.quantity || 1)) || ''),
          hotline: fleet?.phone_number || '',
          notes: fleet?.policy || '',
          passenger: {
            fullName: ticket?.customer_name || data.customer_name || data.passenger?.fullName || '',
            phoneNumber: ticket?.customer_phone || data.customer_phone || data.passenger?.phoneNumber || '',
            email: ticket?.customer_email || data.customer_email || data.passenger?.email || '',
          },
          qrCodeBase64: ticket?.qr_code_image || '',
        };
      }
    } catch (e) {
      console.error('Lỗi parse printData:', e, params.printData);
    }
    
    if (!ticketData) {
      // Mock data với đa ngôn ngữ
      const formatMockDate = () => {
        if (i18n.language === 'vi') {
          return 'Thứ 4, 28/08';
        } else {
          return 'Wed, Aug 28';
        }
      };

      ticketData = {
        ticketId: '**********',
        date: formatMockDate(),
        duration: i18n.language === 'vi' ? '14 giờ' : '14 hours',
        departure: {
            time: '17:00',
            city: i18n.language === 'vi' ? 'Bình Định' : 'Binh Dinh'
        },
        arrival: {
            time: '07:00',
            city: i18n.language === 'vi' ? 'Đà Nẵng' : 'Da Nang'
        },
        busType: t('vehicleType.bed'),
        fleetName: i18n.language === 'vi' ? 'Hãng Xe A' : 'Bus Company A',
        licensePlate: '12A-12345',
        seatNumber: 'B01',
        price: 150000,
        quantity: 2,
        totalAmount: 300000,
        hotline: '1900-xxxx',
        passenger: {
            fullName: i18n.language === 'vi' ? 'Nguyễn Văn A' : 'John Doe',
            phoneNumber: '**********',
            email: '<EMAIL>'
        },
        qrCodeBase64: ''
      };
    }

    // Khi tạo ticketData, override trường date nếu selectedDateStr có giá trị
    if (ticketData && selectedDateStr) {
      ticketData.date = selectedDateStr;
    }

    return (
        <LinearGradient
            colors={['#2D5BFF', '#4A90E2', '#87CEEB', '#F0F8FF']}
            style={styles.gradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
        >
            <SafeAreaView style={styles.container}>
                <StatusBar barStyle="light-content" backgroundColor="#2D5BFF" />

                {/* Header */}
                <View style={styles.headerCard}>
                    <View style={styles.headerContent}>

                        <Text style={styles.headerTitle}>{t('printTicket.title')}</Text>
                        <View style={styles.headerDecoration}>
                            <View style={styles.decorationDot} />
                            <View style={[styles.decorationDot, styles.decorationDotSecondary]} />
                            <View style={[styles.decorationDot, styles.decorationDotTertiary]} />
                        </View>
                    </View>
                </View>

                <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                    <View style={styles.content}>
                        {/* Trip Info Header */}
                        <View style={styles.tripInfoHeader}>
                            <View style={styles.dateContainer}>
                                <MaterialIcons name="calendar-today" size={16} color="#2D5BFF" />
                                <Text style={styles.dateText}>{ticketData.date}</Text>
                            </View>
                            <View style={styles.durationContainer}>
                                <MaterialIcons name="schedule" size={16} color="#2D5BFF" />
                                <Text style={styles.durationText}>{ticketData.duration}</Text>
                            </View>
                        </View>

                        {/* Dashed Line */}
                        <View style={styles.dashedLine} />

                        {/* Route Section */}
                        <View style={styles.routeSection}>
                            <View style={styles.locationContainer}>
                                <Text style={styles.cityCode}>{ticketData.departure.time}</Text>
                                <Text style={styles.cityName}>{ticketData.departure.city}</Text>
                            </View>

                            <View style={styles.busContainer}>
                                <View style={styles.busLine} />
                                <View style={styles.busIcon}>
                                    <MaterialIcons name="directions-bus" size={20} color="white" />
                                </View>
                                <View style={styles.busLine} />
                            </View>

                            <View style={styles.locationContainer}>
                                <Text style={styles.cityCode}>{ticketData.arrival.time}</Text>
                                <Text style={styles.cityName}>{ticketData.arrival.city}</Text>
                            </View>
                        </View>

                        {/* Trip Details */}
                        <View style={styles.tripDetails}>
                            <View style={styles.detailRow}>
                                <View style={styles.detailItem}>
                                    <Text style={styles.detailLabel}>{t('printTicket.busType')}</Text>
                                    <Text style={styles.detailValue}>{ticketData.busType}</Text>
                                </View>
                                <View style={styles.detailItem}>
                                    <Text style={styles.detailLabel}>{t('printTicket.fleetName') || t('ticketConfirmation.busCompany') || 'Hãng xe'}</Text>
                                    <Text style={styles.detailValue}>{ticketData.fleetName}</Text>
                                </View>
                            </View>
                            <View style={styles.detailRow}>
                                <View style={styles.detailItem}>
                                    <Text style={styles.detailLabel}>{t('printTicket.licensePlate') || t('ticketConfirmation.licensePlate') || 'Biển số'}</Text>
                                    <Text style={styles.detailValue}>{ticketData.licensePlate}</Text>
                                </View>
                                <View style={styles.detailItem}>
                                    <Text style={styles.detailLabel}>{t('ticketConfirmation.quantity') || 'Số lượng vé'}</Text>
                                    <Text style={styles.detailValue}>{ticketData.quantity}</Text>
                                </View>
                            </View>
                            <View style={styles.detailRow}>
                                <View style={styles.detailItem}>
                                    <Text style={styles.detailLabel}>{t('ticketConfirmation.totalAmount') || 'Tổng tiền'}</Text>
                                    <Text style={styles.detailValue}>{ticketData.totalAmount ? ticketData.totalAmount.toLocaleString() + ' đ' : ''}</Text>
                                </View>
                                <View style={styles.detailItem}>
                                    <Text style={styles.detailLabel}>{t('printTicket.hotline') || 'Hotline'}</Text>
                                    <Text style={styles.detailValue}>{ticketData.hotline}</Text>
                                </View>
                            </View>
                   
                          
                        </View>

                        {/* Passenger Information */}
                        <View style={styles.passengerInfo}>
                            <View style={styles.sectionHeader}>
                                <MaterialIcons name="person" size={20} color="#2D5BFF" />
                                <Text style={styles.sectionTitle}>{t('printTicket.passengerInfo.title')}</Text>
                            </View>

                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>{t('printTicket.passengerInfo.fullName')}</Text>
                                <Text style={styles.infoValue}>{ticketData.passenger.fullName}</Text>
                            </View>

                            <View style={styles.infoRow}>
                                <Text style={styles.infoLabel}>{t('printTicket.passengerInfo.phoneNumber')}</Text>
                                <Text style={styles.infoValue}>{ticketData.passenger.phoneNumber}</Text>
                            </View>

                            {ticketData.passenger.email ? (
                              <View style={styles.infoRow}>
                                  <Text style={styles.infoLabel}>{t('printTicket.passengerInfo.email')}</Text>
                                  <Text style={styles.infoValue}>{ticketData.passenger.email}</Text>
                              </View>
                            ) : null}
                        </View>

                        {/* Dashed Line */}
                        <View style={styles.dashedLine} />

                        {/* QR Code */}
                        <View style={styles.qrContainer}>
                          {ticketsList.length > 1 ? (
                            <View style={{flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center'}}>
                              {ticketsList.map((tk: any, idx: number) => (
                                <QRCodeCard
                                  key={tk.ticket_number || idx}
                                  qrCodeBase64={tk.qr_code_image}
                                  ticketNumber={tk.ticket_number}
                                  onPress={() => setSelectedQR({ qr: tk.qr_code_image, ticket: tk.ticket_number })}
                                  tapToEnlargeText={t('printTicket.tapToEnlarge')}
                                />
                              ))}
                            </View>
                          ) : (
                            <QRCodeCard
                              qrCodeBase64={ticketData.qrCodeBase64}
                              ticketNumber={ticketData.ticketId}
                              onPress={() => setSelectedQR({ qr: ticketData.qrCodeBase64, ticket: ticketData.ticketId })}
                              tapToEnlargeText={t('printTicket.tapToEnlarge')}
                            />
                          )}
                        </View>

                        {/* Download Button */}
                        <TouchableOpacity
                          style={styles.downloadButton}
                          onPress={() => router.replace('/')}
                        >
                          <MaterialIcons name="check-circle" size={20} color="#fff" style={styles.downloadIcon} />
                          <Text style={styles.downloadButtonText}>
                            {i18n.language === 'vi' ? 'Hoàn thành' : 'Done'}
                          </Text>
                        </TouchableOpacity>
                    </View>
                </ScrollView>

                {/* QR Code Modal cho cả 1 vé và nhiều vé */}
                <Modal
                  visible={!!selectedQR}
                  transparent={true}
                  animationType="fade"
                  onRequestClose={() => setSelectedQR(null)}
                >
                  <TouchableWithoutFeedback onPress={() => setSelectedQR(null)}>
                    <View style={styles.modalOverlay}>
                      <TouchableWithoutFeedback>
                        <Surface style={styles.modalContent} elevation={5}>
                          <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>{t('printTicket.qrCode')}</Text>
                            <TouchableOpacity
                              style={styles.closeButton}
                              onPress={() => setSelectedQR(null)}
                            >
                              <MaterialIcons name="close" size={24} color="#333" />
                            </TouchableOpacity>
                          </View>
                          <View style={styles.enlargedQRContainer}>
                            {selectedQR?.qr ? (
                              <Image
                                source={{ uri: `data:image/png;base64,${selectedQR.qr}` }}
                                style={styles.enlargedQR}
                                resizeMode="contain"
                              />
                            ) : (
                              <QRCode value={selectedQR?.ticket || 'DEFAULT-CODE'} size={250} />
                            )}
                            <Text style={[styles.ticketId, {alignSelf: 'center', marginTop: 16, fontSize: 20}]}>{selectedQR?.ticket}</Text>
                          </View>
                          <View style={styles.modalFooter}>
                            <Text style={styles.modalFooterText}>
                              {t('printTicket.scanQRCode')}
                            </Text>
                          </View>
                        </Surface>
                      </TouchableWithoutFeedback>
                    </View>
                  </TouchableWithoutFeedback>
                </Modal>
            </SafeAreaView>
        </LinearGradient>
    );
};

export default PrintTicketScreen; 
