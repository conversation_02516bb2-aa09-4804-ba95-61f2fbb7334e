import { useTranslation } from 'react-i18next';

export const useGuideTexts = () => {
  const { t } = useTranslation();
  return {
    mainTitle: t('bookingGuide.mainTitle'),
    subtitle: t('bookingGuide.subtitle'),
    step1Title: t('bookingGuide.step1Title'),
    step1Instructions: {
      title: t('bookingGuide.step1InstructionsTitle'),
      steps: t('bookingGuide.step1Instructions', { returnObjects: true })
    },
    step2Title: t('bookingGuide.step2Title'),
    step2Instructions: {
      title: t('bookingGuide.step2InstructionsTitle'),
      steps: t('bookingGuide.step2Instructions', { returnObjects: true })
    },
    step3Title: t('bookingGuide.step3Title'),
    step3Instructions: {
      title: t('bookingGuide.step3InstructionsTitle'),
      steps: t('bookingGuide.step3Instructions', { returnObjects: true })
    },
    step4Title: t('bookingGuide.step4Title'),
    step4Instructions: {
      title: t('bookingGuide.step4InstructionsTitle'),
      steps: t('bookingGuide.step4Instructions', { returnObjects: true })
    },
    step5Title: t('bookingGuide.step5Title'),
    step5Instructions: {
      steps: t('bookingGuide.step5Instructions', { returnObjects: true })
    },
    importantNotes: {
      title: t('bookingGuide.importantNotesTitle'),
      notes: t('bookingGuide.importantNotes', { returnObjects: true })
    },
    contactInfo: {
      title: t('bookingGuide.contactTitle'),
      contacts: [
        { type: 'phone', text: t('bookingGuide.contactPhone') },
        { type: 'facebook', text: t('bookingGuide.contactFacebook') },
        { type: 'email', text: t('bookingGuide.contactEmail') }
      ]
    },
    poweredBy: t('bookingGuide.poweredBy')
  };
};

export const GuideIcons = {
  step1: [
    'touch-app',
    'search', 
    'location-on',
    'check-circle'
  ],
  step2: [
    'schedule',
    'directions-bus',
    'info',
    'policy',
    'event-seat'
  ],
  step3: [
    'confirmation-number',
    'person',
    'check-box',
    'payment'
  ],
  step4: [
    'fact-check', // Kiểm tra thông tin
    'qr-code-scanner', // Quét mã QR để thanh toán
    'print' // Màn hình chuyển sang in vé
  ],
  step5: [
    'print',
    'qr-code-scanner',
    'receipt-long'
  ],
  contact: [
    'phone',
    'facebook',
    'email'
  ]
}; 

