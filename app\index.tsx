import { MaterialIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, BackHandler, Dimensions, Image, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import {
  MD3LightTheme,
  Provider as PaperProvider
} from 'react-native-paper';
import TripInfoModal from '../components/trip/TripInfoModal';
import { Font } from '../constants/Font';

const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#2D5BFF',
    secondary: '#FF6B6B',
    background: '#F8FAFF',
  },
  fonts: {
    displayLarge: { fontFamily: Font.bold, fontWeight: 'bold' },
    displayMedium: { fontFamily: Font.bold, fontWeight: 'bold' },
    displaySmall: { fontFamily: Font.bold, fontWeight: 'bold' },
    headlineLarge: { fontFamily: Font.medium, fontWeight: '500' },
    headlineMedium: { fontFamily: Font.medium, fontWeight: '500' },
    headlineSmall: { fontFamily: Font.medium, fontWeight: '500' },
    titleLarge: { fontFamily: Font.bold, fontWeight: 'bold' },
    titleMedium: { fontFamily: Font.medium, fontWeight: '500' },
    titleSmall: { fontFamily: Font.medium, fontWeight: '500' },
    bodyLarge: { fontFamily: Font.regular, fontWeight: 'normal' },
    bodyMedium: { fontFamily: Font.regular, fontWeight: 'normal' },
    bodySmall: { fontFamily: Font.regular, fontWeight: 'normal' },
    labelLarge: { fontFamily: Font.medium, fontWeight: '500' },
    labelMedium: { fontFamily: Font.medium, fontWeight: '500' },
    labelSmall: { fontFamily: Font.regular, fontWeight: 'normal' },
  },
};

export default function WelcomeScreen() {
  const { width: SCREEN_WIDTH } = Dimensions.get('window');
  const { t, i18n } = useTranslation();

  // Responsive scaling
  const isTablet = SCREEN_WIDTH >= 768;
  const isMobile = SCREEN_WIDTH < 600;
  const scale = SCREEN_WIDTH / 375;
  const buttonScale = Math.min(Math.max(scale, 0.8), 1.5);

  // Dynamic styles
  const dynamicStyles = {
    topHeader: {
      ...styles.topHeader,
      paddingTop: 10 * buttonScale,
      marginBottom: 20 * buttonScale,
      paddingHorizontal: isMobile ? 5 : 10,
    },
    languageButtons: {
      ...styles.languageButtons,
      gap: 8 * buttonScale,
    },
    guideButton: {
      ...styles.guideButton,
      borderRadius: 24 * buttonScale,
      height: 48 * buttonScale,
      paddingHorizontal: (isMobile ? 16 : isTablet ? 32 : 24) * buttonScale,
      minWidth: (isMobile ? 100 : isTablet ? 140 : 120) * buttonScale,
      shadowOffset: { width: 0, height: 2 * buttonScale },
      shadowRadius: 6 * buttonScale,
    },
    guideButtonText: {
      ...styles.guideButtonText,
      fontSize: (isMobile ? 14 : isTablet ? 18 : 16) * buttonScale,
      letterSpacing: (isMobile ? 0.5 : 1) * buttonScale,
      marginLeft: 6 * buttonScale,
    },
  };

  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language || 'vi');
  const [showTermsModal, setShowTermsModal] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const cursorAnim = useRef(new Animated.Value(1)).current;

  // Add pulse animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  // Add cursor blinking animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(cursorAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(cursorAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  // Chặn back trên web, lấp history stack
  useEffect(() => {
    if (Platform.OS === 'web') {
      // Lấp history stack
      for (let i = 0; i < 10; i++) {
        window.history.pushState(null, '', window.location.href);
      }
      const onPopState = (event: PopStateEvent) => {
        window.history.pushState(null, '', window.location.href);
      };
      window.addEventListener('popstate', onPopState);
      return () => {
        window.removeEventListener('popstate', onPopState);
      };
    }
  }, []);

  const handleLanguageChange = (lang: string) => {
    setSelectedLanguage(lang);
    i18n.changeLanguage(lang);
  };

  const handleGuide = () => {
    router.push('/booking-guide');
  };

  const handlePress = () => {
    // Hiệu ứng nhấn
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      })
    ]).start();

    // Chuyển đến màn hình trips
    router.push('/trips');
  };

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => true; // Chặn back
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [])
  );

  return (
    <PaperProvider theme={theme as any}>
      <StatusBar barStyle="light-content" backgroundColor="#2D5BFF" />
      <LinearGradient
        colors={['#2D5BFF', '#4A90E2', '#87CEEB', '#F0F8FF']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.container}>
          {/* Top Header with Language Selection and Guide Button */}
          <View style={dynamicStyles.topHeader}>
            {/* Language Selection - Left Side */}
            <View style={dynamicStyles.languageButtons}>
              {['vi', 'en'].map(lang => (
                <TouchableOpacity
                  key={lang}
                  onPress={() => handleLanguageChange(lang)}
                  style={[
                    {
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minWidth: (isMobile ? 60 : 70) * buttonScale,
                      height: 44 * buttonScale,
                      paddingHorizontal: (isMobile ? 12 : 18) * buttonScale,
                      borderRadius: 22 * buttonScale,
                      backgroundColor: selectedLanguage === lang ? '#fff' : 'rgba(255,255,255,0.15)',
                      borderColor: selectedLanguage === lang ? '#2D5BFF' : 'rgba(255,255,255,0.7)',
                      borderWidth: selectedLanguage === lang ? 2 : 1.5,
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 * buttonScale },
                      shadowOpacity: 0.10,
                      shadowRadius: 4 * buttonScale,
                      elevation: 2,
                      marginHorizontal: 2 * buttonScale,
                    }
                  ]}
                  activeOpacity={0.8}
                >
                  {selectedLanguage === lang && (
                    <MaterialIcons
                      name="check"
                      size={18 * buttonScale}
                      color="#2D5BFF"
                      style={{ marginRight: 4 * buttonScale }}
                    />
                  )}
                  <Text
                    style={{
                      color: selectedLanguage === lang ? '#2D5BFF' : 'rgba(255,255,255,0.95)',
                      fontWeight: '700',
                      fontSize: (isMobile ? 14 : 16) * buttonScale,
                      letterSpacing: (isMobile ? 0.5 : 1) * buttonScale,
                      textAlign: 'center',
                    }}
                  >
                    {lang.toUpperCase()}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Guide Button - Right Side */}
            <TouchableOpacity
              onPress={handleGuide}
              style={dynamicStyles.guideButton}
              activeOpacity={0.8}
            >
              <View style={styles.guideButtonContent}>
                <MaterialIcons
                  name="help-outline"
                  size={(isMobile ? 16 : isTablet ? 22 : 20) * buttonScale}
                  color="#fff"
                />
                <Text style={dynamicStyles.guideButtonText}>
                  {t('welcomeScreen.guide')}
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Main Content */}
          <View style={styles.mainContent}>
            {/* AIVS Logo */}
            <TouchableOpacity
              onPress={handlePress}
              activeOpacity={0.9}
            >
              <Animated.View
                style={[
                  styles.busContainer,
                  {
                    transform: [
                      { scale: Animated.multiply(pulseAnim, scaleAnim) }
                    ]
                  }
                ]}
              >
                <View style={styles.busBackground}>
                  <Image
                    source={require('../assets/images/Gtech-white.png')}
                    style={styles.aivsLogo}
                    resizeMode="contain"
                  />
                </View>
              </Animated.View>
            </TouchableOpacity>
          </View>
        </View>
        <TripInfoModal
          visible={showTermsModal}
          onClose={() => setShowTermsModal(false)}
          type="policy" // hoặc tạo type riêng nếu muốn nội dung khác
        />
      </LinearGradient>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 40,
    justifyContent: 'space-between',
  },

  // Top Header Styles
  topHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 10,
    marginBottom: 20,
    paddingHorizontal: 5,
  },
  languageButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  languageChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderColor: 'rgba(255, 255, 255, 0.7)',
    borderWidth: 1.5,
    minWidth: 70,
    height: 44,
    paddingHorizontal: 18,
    borderRadius: 22, // bo tròn nhiều hơn
    marginHorizontal: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.10,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedChip: {
    backgroundColor: '#fff',
    borderColor: '#2D5BFF',
    borderWidth: 2,
    shadowOpacity: 0.18,
    shadowRadius: 6,
    elevation: 4,
  },
  chipText: {
    color: 'rgba(255, 255, 255, 0.95)',
    fontWeight: '700',
    fontSize: 18,
    letterSpacing: 1,
    textAlign: 'center', // căn giữa chữ
    width: '100%',      // đảm bảo text chiếm hết chiều rộng chip
  },
  selectedChipText: {
    color: '#2D5BFF',
    fontWeight: '700',
    textAlign: 'center', // căn giữa chữ
    width: '100%',
  },

  // Guide Button Styles
  guideButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 24,
    height: 48,
    paddingHorizontal: 20,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 3,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    minWidth: 120,
  },
  guideButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  guideButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 1,
    textAlign: 'center',
    marginLeft: 6,
  },

  // Background Text Styles
  backgroundTextContainer: {
    position: 'absolute',
    top: '20%',
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 0,
  },
  backgroundText: {
    fontSize: 80,
    fontWeight: '900',
    color: 'rgba(255, 255, 255, 0.1)',
    letterSpacing: 8,
    lineHeight: 70,
    textAlign: 'center',
  },

  mainContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    zIndex: 1,
  },

  // AIVS Logo Styles
  busContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  busBackground: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 30,
    padding: 30,
    shadowColor: '#fff',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.35,
    shadowRadius: 15,
    elevation: Platform.OS === 'android' ? 0 : 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  aivsLogo: {
    width: 120,
    height: 120,
  },

  // Title Styles
  titleContainer: {
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  typingLine: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  mainTitle: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
    lineHeight: 52,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 20,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'left',
    marginTop: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  cursor: {
    width: 3,
    height: 48,
    backgroundColor: '#FFFFFF',
    marginLeft: 5,
  },
});

// Nếu dùng expo-router v2+, thêm export này để chặn gesture back trên iOS
export const unstable_settings = {
  gestureEnabled: false,
};
