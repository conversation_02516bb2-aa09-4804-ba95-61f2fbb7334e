import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, Dimensions, StyleSheet, Text, View } from 'react-native';

const { width, height } = Dimensions.get('window');

interface EmptyStateProps {
  title?: string;
  subtitle?: string;
}

export default function EmptyState({ 
  title, 
  subtitle 
}: EmptyStateProps) {
  const { t } = useTranslation();
  
  // Animation values
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;
  const bounceAnimation = useRef(new Animated.Value(0)).current;
  const sparkleAnimation = useRef(new Animated.Value(0)).current;
  const floatAnimation1 = useRef(new Animated.Value(0)).current;
  const floatAnimation2 = useRef(new Animated.Value(0)).current;
  const floatAnimation3 = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start animations
    startAnimations();
  }, []);

  const startAnimations = () => {
    // Fade in and scale up main content
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnimation, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();



    // Bounce animation for main icon
    const bounceLoop = () => {
      Animated.sequence([
        Animated.timing(bounceAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnimation, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setTimeout(bounceLoop, 2000);
      });
    };

    // Sparkle animation
    const sparkleLoop = () => {
      Animated.sequence([
        Animated.timing(sparkleAnimation, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(sparkleAnimation, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]).start(() => sparkleLoop());
    };

    // Floating animations for feature icons
    const createFloatLoop = (animation: Animated.Value, delay: number) => {
      const floatLoop = () => {
        Animated.sequence([
          Animated.timing(animation, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(animation, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ]).start(() => floatLoop());
      };
      setTimeout(floatLoop, delay);
    };

    // Start loops with delays
    setTimeout(bounceLoop, 1000);
    createFloatLoop(floatAnimation1, 500);
    createFloatLoop(floatAnimation2, 1000);
    createFloatLoop(floatAnimation3, 1500);

    // Pulse animation for background circles
    const pulseLoop = () => {
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ]).start(() => pulseLoop());
    };

    sparkleLoop();
    pulseLoop();
  };

  const bounceInterpolate = bounceAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -10],
  });

  const sparkleOpacity = sparkleAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 1, 0.3],
  });

  const sparkleScale = sparkleAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.8, 1.2, 0.8],
  });

  const float1 = floatAnimation1.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -8],
  });

  const float2 = floatAnimation2.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -6],
  });

  const float3 = floatAnimation3.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -10],
  });

  const pulseScale = pulseAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.1],
  });

  const pulseOpacity = pulseAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.05, 0.1, 0.05],
  });

  return (
    <View style={styles.container}>
      {/* Background gradient circles */}
      <View style={styles.backgroundCircles}>
        <Animated.View
          style={[
            styles.circle,
            styles.circle1,
            {
              transform: [{ scale: pulseScale }],
              opacity: pulseOpacity,
            }
          ]}
        />
        <Animated.View
          style={[
            styles.circle,
            styles.circle2,
            {
              transform: [{ scale: pulseScale }],
              opacity: pulseOpacity,
            }
          ]}
        />
        <Animated.View
          style={[
            styles.circle,
            styles.circle3,
            {
              transform: [{ scale: pulseScale }],
              opacity: pulseOpacity,
            }
          ]}
        />
      </View>



      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnimation,
            transform: [{ scale: scaleAnimation }],
          },
        ]}
      >
        <LinearGradient
          colors={['rgba(45, 91, 255, 0.1)', 'rgba(255, 255, 255, 0.9)']}
          style={styles.contentCard}
        >
          {/* Main Icon */}
          <Animated.View
            style={[
              styles.mainIconContainer,
              {
                transform: [{ translateY: bounceInterpolate }],
              },
            ]}
          >
            <LinearGradient
              colors={['#2D5BFF', '#4A90E2']}
              style={styles.mainIconGradient}
            >
              <MaterialIcons name="location-on" size={60} color="#fff" />
              {/* Animated sparkles around main icon */}
              <Animated.View
                style={[
                  styles.sparkleContainer,
                  {
                    opacity: sparkleOpacity,
                    transform: [{ scale: sparkleScale }],
                  },
                ]}
              >
                <Text style={[styles.sparkleIcon, styles.sparkle1]}>✨</Text>
                <Text style={[styles.sparkleIcon, styles.sparkle2]}>⭐</Text>
                <Text style={[styles.sparkleIcon, styles.sparkle3]}>💫</Text>
                <Text style={[styles.sparkleIcon, styles.sparkle4]}>✨</Text>
              </Animated.View>
            </LinearGradient>
          </Animated.View>

          {/* Title */}
          <Text style={styles.title}>{title || t('emptyState.selectToTitle')}</Text>
          
          {/* Subtitle */}
          <Text style={styles.subtitle}>{subtitle || t('emptyState.selectToSubtitle')}</Text>

          {/* Feature Icons */}
          <View style={styles.featureIcons}>
            <Animated.View
              style={[
                styles.featureIcon,
                { transform: [{ translateY: float1 }] }
              ]}
            >
              <MaterialIcons name="schedule" size={24} color="#2D5BFF" />
              <Text style={styles.featureText}>{t('emptyState.fast')}</Text>
            </Animated.View>
            <Animated.View
              style={[
                styles.featureIcon,
                { transform: [{ translateY: float2 }] }
              ]}
            >
              <MaterialIcons name="verified" size={24} color="#4CAF50" />
              <Text style={styles.featureText}>{t('emptyState.reliable')}</Text>
            </Animated.View>
            <Animated.View
              style={[
                styles.featureIcon,
                { transform: [{ translateY: float3 }] }
              ]}
            >
              <MaterialIcons name="local-offer" size={24} color="#FF9800" />
              <Text style={styles.featureText}>{t('emptyState.promotion')}</Text>
            </Animated.View>
          </View>

          {/* Decorative elements */}
          <View style={styles.decorativeElements}>
            <View style={styles.decorativeDot} />
            <View style={styles.decorativeLine} />
            <View style={styles.decorativeDot} />
          </View>
        </LinearGradient>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    position: 'relative',
  },
  backgroundCircles: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  circle: {
    position: 'absolute',
    borderRadius: 1000,
    opacity: 0.05,
  },
  circle1: {
    width: 200,
    height: 200,
    backgroundColor: '#2D5BFF',
    top: '10%',
    left: '10%',
  },
  circle2: {
    width: 150,
    height: 150,
    backgroundColor: '#FF6B6B',
    top: '60%',
    right: '15%',
  },
  circle3: {
    width: 100,
    height: 100,
    backgroundColor: '#4CAF50',
    top: '30%',
    right: '20%',
  },

  content: {
    alignItems: 'center',
    zIndex: 2,
  },
  contentCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 30,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(45, 91, 255, 0.1)',
    width: width * 0.85,
    alignSelf: 'center',
  },
  mainIconContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  mainIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
    position: 'relative',
  },
  sparkleContainer: {
    position: 'absolute',
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sparkleIcon: {
    position: 'absolute',
    fontSize: 16,
  },
  sparkle1: {
    top: -10,
    left: 10,
  },
  sparkle2: {
    top: 10,
    right: -10,
  },
  sparkle3: {
    bottom: -10,
    left: -10,
  },
  sparkle4: {
    bottom: 10,
    right: 10,
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#2D5BFF',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 0.5,
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 17,
    color: '#666',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 35,
    paddingHorizontal: 10,
  },
  featureIcons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 25,
  },
  featureIcon: {
    alignItems: 'center',
    flex: 1,
  },
  featureText: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
    fontWeight: '500',
  },
  decorativeElements: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  decorativeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2D5BFF',
    opacity: 0.3,
  },
  decorativeLine: {
    width: 40,
    height: 2,
    backgroundColor: '#2D5BFF',
    marginHorizontal: 10,
    opacity: 0.3,
  },
});
