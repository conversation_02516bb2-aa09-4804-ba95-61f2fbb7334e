import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, Dimensions, Easing, Modal, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { Surface } from 'react-native-paper';

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: FilterOptions) => void;
}

export interface FilterOptions {
  // Đổi lại để phù hợp với UI mới
  departureTimeSlots: string[]; // VD: ['early', 'morning']
  priceRange: [number, number];
  busTypes: string[];
  sortOption: SortOption;
}

export type SortOption = 'priceDesc' | 'priceAsc' | 'departureEarliest' | 'departureLatest';

const { height, width } = Dimensions.get('window');

const FilterModal = ({ visible, onClose, onApplyFilters }: FilterModalProps) => {
  const { t } = useTranslation();
  const slideAnim = useRef(new Animated.Value(height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Default filter values
  const [priceRange, setPriceRange] = useState<[number, number]>([100000, 1000000]);
  const [selectedBusTypes, setSelectedBusTypes] = useState<string[]>([]);
  const [selectedTimeSlots, setSelectedTimeSlots] = useState<string[]>([]);
  const [selectedSort, setSelectedSort] = useState<SortOption | ''>('');

  const TIME_SLOTS = [
    { key: 'early', label: t('filterModal.earlyMorning') },
    { key: 'morning', label: t('filterModal.morning') },
    { key: 'afternoon', label: t('filterModal.afternoon') },
    { key: 'evening', label: t('filterModal.evening') },
  ];
  const SORT_OPTIONS = [
    { key: '', label: t('filterModal.defaultSort') },
    { key: 'priceDesc', label: t('filterModal.priceDesc') },
    { key: 'priceAsc', label: t('filterModal.priceAsc') },
    { key: 'departureEarliest', label: t('filterModal.departureEarliest') },
    { key: 'departureLatest', label: t('filterModal.departureLatest') },
  ];
  const busTypes = [
    { id: 'chair', name: t('filterModal.seat') },
    { id: 'bed', name: t('filterModal.bed') },
    { id: 'limousine', name: t('filterModal.limousine') },
  ];

  useEffect(() => {
    if (visible) {
      // Hiển thị overlay trước
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }).start();

      // Sau đó trượt modal lên
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }).start();
    } else {
      // Trượt modal xuống trước
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 400,
        useNativeDriver: true,
        easing: Easing.in(Easing.cubic),
      }).start();

      // Sau đó ẩn overlay
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.in(Easing.ease),
      }).start();
    }
  }, [visible, slideAnim, fadeAnim]);

  const toggleBusType = (id: string) => {
    if (selectedBusTypes.includes(id)) {
      setSelectedBusTypes(selectedBusTypes.filter(type => type !== id));
    } else {
      setSelectedBusTypes([...selectedBusTypes, id]);
    }
  };

  const toggleTimeSlot = (key: string) => {
    setSelectedTimeSlots(prev => prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]);
  };

  const formatTime = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
  };

  const formatPrice = (price: number) => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + 'đ';
  };

  const toTimeString = (hour: number) => {
    if (hour >= 24) return '23:59';
    const h = Math.floor(hour);
    const m = Math.round((hour - h) * 60);
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
  };

  const handleApply = () => {
    onApplyFilters({
      departureTimeSlots: selectedTimeSlots,
      priceRange,
      busTypes: selectedBusTypes,
      sortOption: selectedSort as SortOption,
    });
    onClose();
  };

  // Thêm hàm reset filter
  const handleReset = () => {
    setSelectedSort('');
    setSelectedTimeSlots([]);
    setSelectedBusTypes([]);
    setPriceRange([100000, 1000000]);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View
            style={[
              styles.overlay,
              { opacity: fadeAnim }
            ]}
          />
        </TouchableWithoutFeedback>

        <Animated.View
          style={[
            styles.contentContainer,
            { transform: [{ translateY: slideAnim }] }
          ]}
        >
          <Surface style={styles.container} elevation={5}>
            <View style={styles.header}>
              <TouchableOpacity onPress={handleReset} style={styles.resetButton}>
                <Ionicons name="refresh" size={18} color="#2D5BFF" />
                <Text style={styles.resetButtonText}>{t('filterModal.reset')}</Text>
              </TouchableOpacity>
              <Text style={styles.title}>{t('filterModal.title')}</Text>
              <TouchableOpacity onPress={onClose}>
                <Ionicons name="close" size={22} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Sort section */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>{t('filterModal.sort')}</Text>
              {SORT_OPTIONS.map(opt => (
                <TouchableOpacity key={opt.key} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }} onPress={() => setSelectedSort(opt.key as SortOption)}>
                  <MaterialIcons name={selectedSort === opt.key ? 'radio-button-checked' : 'radio-button-unchecked'} size={22} color="#2D5BFF" />
                  <Text style={{ marginLeft: 10, fontSize: 16 }}>{opt.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
            {/* Time slot section */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>{t('filterModal.departureTime')}</Text>
              {TIME_SLOTS.map(slot => (
                <TouchableOpacity key={slot.key} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }} onPress={() => toggleTimeSlot(slot.key)}>
                  <MaterialIcons name={selectedTimeSlots.includes(slot.key) ? 'check-box' : 'check-box-outline-blank'} size={22} color="#2D5BFF" />
                  <Text style={{ marginLeft: 10, fontSize: 16 }}>{slot.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
            {/* Bus Types */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>{t('filterModal.busType')}</Text>
              <View style={styles.busTypesRowContainer}>
                {busTypes.map((type) => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.busTypeTag,
                      selectedBusTypes.includes(type.id) && styles.busTypeTagSelected
                    ]}
                    onPress={() => toggleBusType(type.id)}
                  >
                    <Text
                      style={[
                        styles.busTypeTagText,
                        selectedBusTypes.includes(type.id) && styles.busTypeTagTextSelected
                      ]}
                    >
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Apply Button */}
            <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
              <Text style={styles.applyButtonText}>{t('common.apply')}</Text>
            </TouchableOpacity>
          </Surface>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.18)',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.18)',
  },
  contentContainer: {
    width: '100%',
  },
  container: {
    backgroundColor: '#F8FAFF',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    paddingHorizontal: 22,
    paddingBottom: 28,
    paddingTop: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -8 },
    shadowOpacity: 0.10,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 0,
    marginBottom: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D5BFF',
    flex: 1,
    textAlign: 'center',
  },
  filterSection: {
    marginTop: 18,
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: '#222',
    marginBottom: 10,
    letterSpacing: 0.1,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  timeBox: {
    width: '48%',
  },
  timeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  timeValue: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 10,
    borderRadius: 8,
  },
  timeText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
  },
  sliderTrack: {
    height: 4,
    borderRadius: 2,
  },
  sliderThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#2D5BFF',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  priceRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  priceValue: {
    fontSize: 16,
    color: '#2D5BFF',
    fontWeight: '600',
  },
  busTypesContainer: {
    marginTop: 8,
  },
  busTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedBusType: {
    borderColor: '#2D5BFF',
    backgroundColor: 'rgba(45, 91, 255, 0.05)',
  },
  checkIcon: {
    marginRight: 8,
  },
  busTypeText: {
    fontSize: 16,
    color: '#333',
  },
  selectedBusTypeText: {
    color: '#2D5BFF',
    fontWeight: '600',
  },
  // Tag loại xe
  busTypesRowContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 4,
  },
  busTypeTag: {
    borderWidth: 1.5,
    borderColor: '#D0D4DD',
    borderRadius: 14,
    paddingVertical: 8,
    paddingHorizontal: 18,
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#fff',
    minWidth: 70,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#2D5BFF11',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 1,
  },
  busTypeTagSelected: {
    borderColor: '#2D5BFF',
    backgroundColor: '#E9F1FF',
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 2,
  },
  busTypeTagText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: 0.1,
  },
  busTypeTagTextSelected: {
    color: '#2D5BFF',
    fontWeight: 'bold',
  },
  // Radio/checkbox
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    paddingVertical: 4,
    borderRadius: 10,
    paddingHorizontal: 4,
  },
  radioIcon: {
    marginRight: 10,
  },
  radioText: {
    fontSize: 14,
    color: '#222',
    fontWeight: '500',
  },
  // Nút áp dụng
  applyButton: {
    backgroundColor: '#2D5BFF',
    borderRadius: 16,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 18,
    shadowColor: '#2D5BFF33',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 3,
  },
  applyButtonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: 'bold',
    letterSpacing: 0.2,
  },
  // Nút mặc định
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#2D5BFF',
    backgroundColor: '#E9F1FF',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 14,
    marginRight: 10,
  },
  resetButtonText: {
    color: '#2D5BFF',
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 4,
  },
});

export default FilterModal;
