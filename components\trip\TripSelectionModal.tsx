import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';

import { format, getDate, getDay, getDaysInMonth, startOfMonth } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Modal, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button, IconButton, Surface, Text, TextInput } from 'react-native-paper';
import { getAllPlaces } from '../../api/placeService';

export interface Place {
  id: number;
  name: string;
}

interface TripSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSearch: (from: Place, to: Place) => void;
}

const TripSelectionModal = ({ visible, onClose, onSearch }: TripSelectionModalProps) => {
  const { t, i18n } = useTranslation();
  const dateLocale = i18n.language === 'vi' ? vi : enUS;
  const [departureCity, setDepartureCity] = useState<Place | null>({ id: 24, name: 'Hà Nội' });
  const [destinationCity, setDestinationCity] = useState<Place | null>({ id: 42, name: 'Ninh Bình' });
  const [showDepartureDialog, setShowDepartureDialog] = useState(false);
  const [showDestinationDialog, setShowDestinationDialog] = useState(false);

  const [places, setPlaces] = useState<Place[]>([]);
  const [loadingPlaces, setLoadingPlaces] = useState(false);
  const [errorPlaces, setErrorPlaces] = useState<string | null>(null);

  const [departureSearch, setDepartureSearch] = useState('');
  const [destinationSearch, setDestinationSearch] = useState('');
  const [date, setDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());

  useEffect(() => {
    if (visible) {
      setLoadingPlaces(true);
      getAllPlaces()
        .then((res: { data: Place[] }) => {
          setPlaces(res.data);
          setLoadingPlaces(false);
        })
        .catch((err: unknown) => {
          setErrorPlaces("Không thể tải danh sách điểm đi/điểm đến");
          setLoadingPlaces(false);
        });
    }
  }, [visible]);

  const DEPARTURE_CITIES = places;
  const DESTINATION_CITIES = places;

  const filteredDepartureCities = DEPARTURE_CITIES.filter(city =>
    city.name.toLowerCase().includes(departureSearch.toLowerCase())
  );
  const filteredDestinationCities = DESTINATION_CITIES.filter(city =>
    city.name.toLowerCase().includes(destinationSearch.toLowerCase())
  );

  const handleSearch = () => {
    if (departureCity && destinationCity) {
      onSearch(departureCity, destinationCity);
      onClose();
    }
  };

  // Đóng modal và reset về view chính
  const handleClose = () => {
    setShowDepartureDialog(false);
    setShowDestinationDialog(false);
    onClose();
  };

  const renderCityItem = (city: Place, onSelect: (city: Place) => void) => (
    <TouchableOpacity
      style={styles.cityItem}
      onPress={() => onSelect(city)}
    >
      <MaterialCommunityIcons name="city-variant-outline" size={26} color="#2D5BFF" style={{marginRight: 12}} />
      <Text style={styles.cityItemText}>{city.name}</Text>
    </TouchableOpacity>
  );

  // Tạo lịch
  const renderCalendar = () => {
    const today = new Date();
    const monthStart = startOfMonth(currentMonth);
    const daysInMonth = getDaysInMonth(currentMonth);
    const startDay = getDay(monthStart);
    
    const weekDays = i18n.language === 'vi' 
      ? ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'] 
      : ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    
    // Tạo mảng các ngày trong tháng
    const days = [];
    
    // Thêm ngày trống cho các ngày đầu tuần
    for (let i = 0; i < startDay; i++) {
      days.push(
        <View key={`empty-${i}`} style={styles.calendarDay}>
          <Text style={styles.calendarDayTextDisabled}> </Text>

        </View>
      );
    }
    
    // Thêm các ngày trong tháng
    for (let i = 1; i <= daysInMonth; i++) {
      const currentDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), i);
      const isSelected = getDate(date) === i && 
                         date.getMonth() === currentMonth.getMonth() && 
                         date.getFullYear() === currentMonth.getFullYear();
      const isPast = currentDate < today && !(currentDate.getDate() === today.getDate() && 
                                             currentDate.getMonth() === today.getMonth() && 
                                             currentDate.getFullYear() === today.getFullYear());
      
      days.push(
        <TouchableOpacity 
          key={`day-${i}`} 
          style={[
            styles.calendarDay, 
            isSelected && styles.calendarDaySelected,
            isPast && styles.calendarDayDisabled
          ]}
          onPress={() => {
            if (!isPast) {
              setDate(new Date(currentMonth.getFullYear(), currentMonth.getMonth(), i));
            }
          }}
          disabled={isPast}
        >
          <Text style={[
            styles.calendarDayText, 
            isSelected && styles.calendarDayTextSelected,
            isPast && styles.calendarDayTextDisabled
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }
    
    return (
      <View style={styles.calendarContainer}>
        <View style={styles.calendarHeader}>
          <IconButton 
            icon="chevron-left" 
            size={24} 
            onPress={() => {
              const newMonth = new Date(currentMonth);
              newMonth.setMonth(newMonth.getMonth() - 1);
              setCurrentMonth(newMonth);
            }}
          />
          <Text style={styles.calendarTitle}>
            {i18n.language === 'vi' 
              ? `Tháng ${format(currentMonth, 'MM yyyy')}` 
              : format(currentMonth, 'MMMM yyyy', { locale: dateLocale })}
          </Text>
          <IconButton 
            icon="chevron-right" 
            size={24} 
            onPress={() => {
              const newMonth = new Date(currentMonth);
              newMonth.setMonth(newMonth.getMonth() + 1);
              setCurrentMonth(newMonth);
            }}
          />
        </View>
        
        <View style={styles.calendarWeekDays}>
          {weekDays.map((day, index) => (
            <View key={`weekday-${index}`} style={styles.calendarWeekDay}>
              <Text style={styles.calendarWeekDayText}>{day}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.calendarDays}>
          {days}
        </View>
      </View>
    );
  };

  // Format ngày đã chọn theo ngôn ngữ
  const formatSelectedDate = () => {
    if (i18n.language === 'vi') {
      const dayName = format(date, 'EEEE', { locale: dateLocale });
      const capitalizedDay = dayName.charAt(0).toUpperCase() + dayName.slice(1);
      return `Thứ ${capitalizedDay}, ${format(date, 'dd/MM/yyyy')}`;
    } else {
      return format(date, 'EEEE, MMMM dd, yyyy', { locale: dateLocale });
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}
    >
      <TouchableOpacity 
        style={styles.centeredView} 
        activeOpacity={1} 
        onPress={handleClose}
      >
        <TouchableOpacity 
          style={styles.modalView}
          activeOpacity={1} 
          onPress={(e) => e.stopPropagation()}
        >
          <Surface style={styles.modalView} elevation={5}>
          <LinearGradient
            colors={['#2D5BFF', '#4A90E2']}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.modalTitle}>
              {t('tripSelection.title')}
            </Text>
            <IconButton
              icon={() => <MaterialCommunityIcons name="close-circle-outline" size={28} color="#FFF" />}
              onPress={handleClose}
              style={styles.closeButton}
            />
          </LinearGradient>
          {/* Hiển thị form chọn chuyến khi không hiển thị dialog */}
          {!showDepartureDialog && !showDestinationDialog && (
            <View style={styles.contentContainer}>
              <View style={styles.locationContainer}>
                {/* Departure */}
                <Surface style={styles.locationCard} elevation={2}>
                  <TouchableOpacity 
                    style={styles.locationItem}
                    onPress={() => setShowDepartureDialog(true)}
                  >
                    <View style={[styles.locationIcon, styles.departureIcon]}>
                      <MaterialIcons name="location-on" size={28} color="#2D5BFF" />
                    </View>
                    <View style={styles.locationTextContainer}>
                      <Text style={styles.locationLabel}>{t('tripSelection.departure')}</Text>
                      <Text style={styles.locationName}>{departureCity?.name || ''}</Text>
                    </View>
                    <MaterialIcons name="chevron-right" size={28} color="#2D5BFF" />
                  </TouchableOpacity>
                </Surface>
                {/* Destination */}
                <Surface style={styles.locationCard} elevation={2}>
                  <TouchableOpacity 
                    style={styles.locationItem}
                    onPress={() => setShowDestinationDialog(true)}
                  >
                    <View style={[styles.locationIcon, styles.destinationIcon]}>
                      <MaterialIcons name="location-on" size={28} color="#FF6B6B" />
                    </View>
                    <View style={styles.locationTextContainer}>
                      <Text style={styles.locationLabel}>{t('tripSelection.destination')}</Text>
                      <Text style={styles.locationName}>{destinationCity?.name || ''}</Text>
                    </View>
                    <MaterialIcons name="chevron-right" size={28} color="#FF6B6B" />
                  </TouchableOpacity>
                </Surface>
              </View>
              <View style={styles.dateContainer}>
                <View style={styles.dateIconContainer}>
                  <MaterialIcons name="calendar-today" size={24} color="#2D5BFF" />
                </View>
                <View style={styles.dateTextContainer}>
                  <Text style={styles.dateLabel}>{t('tripSelection.departureDate')}</Text>
                  <Text style={styles.dateValue}>{formatSelectedDate()}</Text>
                </View>
              </View>
              <Button
                mode="contained"
                onPress={handleSearch}
                style={styles.searchButton}
                labelStyle={styles.searchButtonText}
                icon="magnify"
              >
                {t('tripSelection.search')}
              </Button>
            </View>
          )}
          {/* Departure City Selection Dialog */}
          {showDepartureDialog && (
            <View style={styles.dialogContainer}>
              <View style={styles.dialogHeader}>
                <Text style={styles.dialogTitle}>{t('tripSelection.selectDeparture')}</Text>
              </View>
              <TextInput
                placeholder={t('tripSelection.searchPlaceholder')}
                value={departureSearch}
                onChangeText={setDepartureSearch}
                style={styles.searchInput}
                left={<TextInput.Icon icon={() => <MaterialCommunityIcons name="map-search-outline" size={22} color="#2D5BFF" />} />}
                mode="outlined"
              />
              <FlatList
                data={filteredDepartureCities}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <Surface style={styles.cityItemCard} elevation={1}>
                    {renderCityItem(item, (city) => {
                      setDepartureCity(city);
                      setShowDepartureDialog(false);
                      setDepartureSearch('');
                    })}
                  </Surface>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                style={styles.cityList}
                showsVerticalScrollIndicator={false}
              />
              <Button 
                mode="contained"
                onPress={() => { setShowDepartureDialog(false); setDepartureSearch(''); }}
                style={styles.backButton}
                labelStyle={styles.backButtonText}
                icon={() => <MaterialCommunityIcons name="arrow-left-bold" size={22} color="#FFF" />}
              >
                {t('tripSelection.back')}
              </Button>
            </View>
          )}
          {/* Destination City Selection Dialog */}
          {showDestinationDialog && (
            <View style={styles.dialogContainer}>
              <View style={styles.dialogHeader}>
                <Text style={styles.dialogTitle}>{t('tripSelection.selectDestination')}</Text>
              </View>
              <TextInput
                placeholder={t('tripSelection.searchPlaceholder')}
                value={destinationSearch}
                onChangeText={setDestinationSearch}
                style={styles.searchInput}
                left={<TextInput.Icon icon={() => <MaterialCommunityIcons name="map-search-outline" size={22} color="#FF6B6B" />} />}
                mode="outlined"
              />
              <FlatList
                data={filteredDestinationCities}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <Surface style={styles.cityItemCard} elevation={1}>
                    {renderCityItem(item, (city) => {
                      setDestinationCity(city);
                      setShowDestinationDialog(false);
                      setDestinationSearch('');
                    })}
                  </Surface>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                style={styles.cityList}
                showsVerticalScrollIndicator={false}
              />
              <Button 
                mode="contained"
                onPress={() => { setShowDestinationDialog(false); setDestinationSearch(''); }}
                style={styles.backButton}
                labelStyle={styles.backButtonText}
                icon={() => <MaterialCommunityIcons name="arrow-left-bold" size={22} color="#FFF" />}
              >
                {t('tripSelection.back')}
              </Button>
            </View>
          )}
        </Surface>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  modalView: {
    width: '85%',
    maxWidth: 600,
    alignSelf: 'center',
    borderRadius: 32,
  },
  headerGradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    margin: 0,
  },
  contentContainer: {
    padding: 20,
  },
  locationContainer: {
    marginBottom: 24,
  },
  locationCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  locationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  departureIcon: {
    backgroundColor: 'rgba(45, 91, 255, 0.1)',
  },
  destinationIcon: {
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  locationTextContainer: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  locationName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  dateIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(78, 205, 196, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  dateTextContainer: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  dateValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  searchButton: {
    borderRadius: 12,
    paddingVertical: 8,
    backgroundColor: '#2D5BFF',
  },
  searchButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  dialogContainer: {
    padding: 20,
  },
  dialogHeader: {
    marginBottom: 16,
  },
  dialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  cityList: {
    maxHeight: 300,
    marginBottom: 16,
  },
  cityItemCard: {
    borderRadius: 20,
    marginBottom: 12,
    backgroundColor: '#F8FAFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  cityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 20,
  },
  cityItemText: {
    fontSize: 16,
    color: '#333',
  },
  separator: {
    height: 1,
    backgroundColor: '#E5E5E5',
    marginVertical: 4,
  },
  backButton: {
    borderRadius: 20,
    paddingVertical: 8,
    backgroundColor: '#2D5BFF',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  calendarContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  calendarTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  calendarWeekDays: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  calendarWeekDay: {
    width: 40,
    alignItems: 'center',
    padding: 8,
  },
  calendarWeekDayText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  calendarDays: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  calendarDay: {
    width: '14.28%',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4,
  },
  calendarDaySelected: {
    backgroundColor: '#2D5BFF',
    borderRadius: 20,
  },
  calendarDayDisabled: {
    opacity: 0.3,
  },
  calendarDayText: {
    fontSize: 16,
    color: '#333',
  },
  calendarDayTextSelected: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  calendarDayTextDisabled: {
    color: '#999',
  },
  selectedDateContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  selectedDateLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  selectedDateValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  searchInput: {
    marginBottom: 12,
    borderRadius: 20,
    backgroundColor: '#F0F4FF',
  },
});

export default TripSelectionModal;

