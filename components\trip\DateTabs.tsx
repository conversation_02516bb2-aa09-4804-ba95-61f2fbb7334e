import { addDays, format, isToday } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface DateTabsProps {
  onSelectDate: (date: Date) => void;
  selectedDate?: Date;
}

const DateTabs = ({ onSelectDate, selectedDate }: DateTabsProps) => {
  const { t, i18n } = useTranslation();
  const [locale, setLocale] = useState(i18n.language === 'vi' ? vi : enUS);

  useEffect(() => {
    setLocale(i18n.language === 'vi' ? vi : enUS);
  }, [i18n.language]);
  const [dates, setDates] = useState<{
    date: Date;
    active: boolean;
  }[]>([]);

  useEffect(() => {
    const today = new Date();
    const numDays = 30; // Số ngày muốn hiển thị
    const daysArr = Array(numDays).fill(0).map((_, index) => {
      const date = addDays(today, index);
      return {
        date,
        active: selectedDate ?
          format(date, 'dd/MM/yyyy') === format(selectedDate, 'dd/MM/yyyy') :
          index === 0
      };
    });
    setDates(daysArr);

    // Select today by default if no date is selected
    if (!selectedDate) {
      onSelectDate(today);
    }
  }, [selectedDate, onSelectDate]);

  const handleSelectDate = (date: Date, index: number) => {
    onSelectDate(date);
  };

  const renderDateTab = ({ item, index }: { item: { date: Date; active: boolean }, index: number }) => {
    const dayName = format(item.date, 'EEEEEE', { locale }).toUpperCase();
    const dayNumber = format(item.date, 'dd');
    const monthName = format(item.date, 'MMM', { locale });
    const isCurrentDay = isToday(item.date);

    return (
      <TouchableOpacity
        onPress={() => handleSelectDate(item.date, index)}
        activeOpacity={0.7}
        style={styles.tabContainer}
      >
        {item.active ? (
          <LinearGradient
            colors={['#2D5BFF', '#4A90E2']}
            style={styles.activeDateTab}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.activeDateTabDay}>{dayName}</Text>
            <Text style={styles.activeDateTabNumber}>{dayNumber}</Text>
            {isCurrentDay ? (
              <View style={styles.activeTodayIndicator}>
                <Text style={styles.activeTodayText}>{t('common.today', 'Today')}</Text>
              </View>
            ) : (
              <Text style={styles.activeDateTabMonth}>{monthName}</Text>
            )}
          </LinearGradient>
        ) : (
          <View style={styles.dateTab}>
            <Text style={styles.dateTabDay}>{dayName}</Text>
            <Text style={styles.dateTabNumber}>{dayNumber}</Text>
            {isCurrentDay ? (
              <View style={styles.todayIndicator}>
                <Text style={styles.todayText}>{t('common.today', 'Today')}</Text>
              </View>
            ) : (
              <Text style={styles.dateTabMonth}>{monthName}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={{ borderRadius: 20, overflow: 'hidden', backgroundColor: '#f6f9ff', minHeight: 100 }}>
      <FlatList
        data={dates}
        renderItem={renderDateTab}
        keyExtractor={(item, index) => `date-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.dateTabsList}
        ListHeaderComponent={<View style={{ width: 12 }} />}
        ListFooterComponent={<View style={{ width: 12 }} />}
        scrollEnabled={true}
        bounces={true}
        decelerationRate="fast"
        snapToInterval={76} // width + margin
        snapToAlignment="start"
        removeClippedSubviews={false}
        initialNumToRender={30}
        maxToRenderPerBatch={30}
        windowSize={30}
        getItemLayout={(data, index) => ({
          length: 76,
          offset: 76 * index,
          index,
        })}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  dateTabsList: {
    paddingVertical: 6,
    paddingHorizontal: 0,
  },
  tabContainer: {
    marginHorizontal: 4,
  },
  dateTab: {
    width: 68,
    height: 88,
    borderRadius: 16,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: '#E3E9F4',
  },
  activeDateTab: {
    width: 68,
    height: 88,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: undefined,
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    elevation: 3,
  },
  dateTabDay: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    marginBottom: 2,
    textAlign: 'center',
  },
  dateTabNumber: {
    fontSize: 24,
    color: '#222',
    fontWeight: 'bold',
    marginBottom: 2,
    textAlign: 'center',
  },
  dateTabMonth: {
    fontSize: 11,
    color: '#888',
    textAlign: 'center',
  },
  activeDateTabDay: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
    marginBottom: 2,
    textAlign: 'center',
  },
  activeDateTabNumber: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 2,
    textAlign: 'center',
  },
  activeDateTabMonth: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  todayIndicator: {
    backgroundColor: 'rgba(45, 91, 255, 0.15)',
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(45, 91, 255, 0.3)',
  },
  todayText: {
    fontSize: 9,
    color: '#2D5BFF',
    fontWeight: '700',
  },
  activeTodayIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeTodayText: {
    fontSize: 9,
    color: '#FFFFFF',
    fontWeight: '700',
  },
});

export default DateTabs;



