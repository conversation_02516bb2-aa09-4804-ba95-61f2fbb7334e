import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { IconButton, RadioButton, Surface, Text } from 'react-native-paper';

interface Location {
  id: string;
  name: string;
  address: string;
  time: string;
}

interface PickupDropoffModalProps {
  visible: boolean;
  onClose: () => void;
  type: 'pickup' | 'dropoff';
  locations: Location[];
  selectedLocationId: string;
  onSelectLocation: (locationId: string) => void;
}

const PickupDropoffModal = ({ 
  visible, 
  onClose, 
  type, 
  locations, 
  selectedLocationId, 
  onSelectLocation 
}: PickupDropoffModalProps) => {
  const { t } = useTranslation();
  const [selected, setSelected] = useState(selectedLocationId);

  const handleSelect = (locationId: string) => {
    setSelected(locationId);
  };

  const handleConfirm = () => {
    onSelectLocation(selected);
    onClose();
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <Surface style={styles.modalView} elevation={5}>
          <LinearGradient
            colors={['#2D5BFF', '#4A90E2']}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.modalTitle}>
              {type === 'pickup' ? t('passengerInfo.selectPickup') : t('passengerInfo.selectDropoff')}
            </Text>
            <IconButton
              icon="close"
              iconColor="#FFFFFF"
              size={24}
              onPress={onClose}
              style={styles.closeButton}
            />
          </LinearGradient>
          
          <ScrollView style={styles.contentContainer}>
            {locations.map((location, index) => (
              <TouchableOpacity 
                key={location.id} 
                style={styles.locationItem}
                onPress={() => handleSelect(location.id)}
              >
                <RadioButton
                  value={location.id}
                  status={selected === location.id ? 'checked' : 'unchecked'}
                  onPress={() => handleSelect(location.id)}
                  color="#2D5BFF"
                />
                <View style={styles.locationContent}>
                  <View style={styles.locationHeader}>
                    <MaterialIcons 
                      name={type === 'pickup' ? 'location-on' : 'location-off'} 
                      size={20} 
                      color={type === 'pickup' ? '#4CAF50' : '#FF6B6B'} 
                    />
                    <Text style={styles.locationLabel}>
                      {type === 'pickup' ? t('passengerInfo.pickupPoint') : t('passengerInfo.dropoffPoint')} {index + 1}
                    </Text>
                  </View>
                  <Text style={styles.locationTime}>{location.time} - {location.name}</Text>
                  <Text style={styles.locationAddress}>{location.address}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <View style={styles.footer}>
            <TouchableOpacity 
              style={styles.confirmButton}
              onPress={handleConfirm}
            >
              <Text style={styles.confirmButtonText}>{t('common.confirm')}</Text>
            </TouchableOpacity>
          </View>
        </Surface>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  modalView: {
    width: '65%',
    maxHeight: '60%',
    backgroundColor: '#fff',
    borderRadius: 32,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 10,
  },
  headerGradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 18,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 0.5,
  },
  closeButton: {
    margin: 0,
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 20,
  },
  contentContainer: {
    padding: 18,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F4F7FF',
    borderRadius: 18,
    paddingVertical: 16,
    paddingHorizontal: 14,
    marginBottom: 14,
    shadowColor: '#2D5BFF22',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  locationContent: {
    flex: 1,
    marginLeft: 12,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  locationLabel: {
    fontSize: 15,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  locationTime: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D5BFF',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 15,
    color: '#888',
  },
  footer: {
    padding: 18,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    backgroundColor: '#fff',
  },
  confirmButton: {
    backgroundColor: '#2D5BFF',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#2D5BFF44',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 18,
    letterSpacing: 0.5,
  },
});

export default PickupDropoffModal;


