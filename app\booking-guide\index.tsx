import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dimensions, Image, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { GuideIcons, useGuideTexts } from './GuideTexts';

export default function BookingGuide() {
  const { t, i18n } = useTranslation();
  const lang = i18n.language;

  // Hàm lấy ảnh từng bước theo ngôn ngữ
  const images = {
    vi: [
      require('../../assets/images/kiosk-step1.png'),
      require('../../assets/images/kiosk-step2.png'),
      require('../../assets/images/kiosk-step3.png'),
      require('../../assets/images/kiosk-step4.png'),
      require('../../assets/images/kiosk-step5.png'),
    ],
    en: [
      require('../../assets/images/kiosk-step1-en.png'),
      require('../../assets/images/kiosk-step2-en.png'),
      require('../../assets/images/kiosk-step3-en.png'),
      require('../../assets/images/kiosk-step4-en.png'),
      require('../../assets/images/kiosk-step5-en.png'),
    ]
  };
  const getStepImage = (step: number) => {
    const arr = lang === 'en' ? images.en : images.vi;
    return arr[step - 1];
  };
  const SCREEN_WIDTH = Dimensions.get('window').width;
  const SCREEN_HEIGHT = Dimensions.get('window').height;
  const isMobile = SCREEN_WIDTH < 600;
  const GuideTexts = useGuideTexts();

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#2D5BFF" />
      <LinearGradient
        colors={['#2D5BFF', '#4A90E2', '#87CEEB', '#F0F8FF']}
        style={{ flex: 1, position: 'relative' }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <MaterialIcons name="arrow-back" size={32} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView 
          style={styles.container}
          contentContainerStyle={{ paddingBottom: 40 }}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.mainTitle}>{GuideTexts.mainTitle}</Text>
          <Text style={styles.subtitle}>{GuideTexts.subtitle}</Text>

          {/* Bước 1: Chọn điểm đến */}
          <View style={styles.stepContainer}>
            <View style={styles.stepHeader}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <Text style={styles.stepTitle}>{GuideTexts.step1Title}</Text>
            </View>
            
            <View style={styles.imageContainer}>
              <Image
                source={getStepImage(1)}
                style={styles.guideImage}
                resizeMode="contain"
              />
            </View>

            <View style={styles.instructionContainer}>
              {GuideTexts.step1Instructions.title && (
                <Text style={styles.instructionTitle}>{GuideTexts.step1Instructions.title}</Text>
              )}
              <View style={styles.instructionList}>
                {(GuideTexts.step1Instructions.steps as string[]).map((step: string, index: number) => (
                  <View key={index} style={styles.instructionItemRow}>
                    <Text style={styles.instructionNumber}>{index + 1}.</Text>
                    <Text style={styles.instructionText}>{step}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* Bước 2: Chọn chuyến xe */}
          <View style={styles.stepContainer}>
            <View style={styles.stepHeader}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <Text style={styles.stepTitle}>{GuideTexts.step2Title}</Text>
            </View>
            
            <View style={styles.imageContainer}>
              <Image
                source={getStepImage(2)}
                style={styles.guideImage}
                resizeMode="contain"
              />
            </View>

            <View style={styles.instructionContainer}>
              {GuideTexts.step2Instructions.title && (
                <Text style={styles.instructionTitle}>{GuideTexts.step2Instructions.title}</Text>
              )}
              <View style={styles.instructionList}>
                {(GuideTexts.step2Instructions.steps as string[]).map((step: string, index: number) => (
                  <View key={index} style={styles.instructionItemRow}>
                    <Text style={styles.instructionNumber}>{index + 1}.</Text>
                    <Text style={styles.instructionText}>{step}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* Bước 3: Nhập thông tin & thanh toán */}
          <View style={styles.stepContainer}>
            <View style={styles.stepHeader}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <Text style={styles.stepTitle}>{GuideTexts.step3Title}</Text>
            </View>
            <View style={styles.imageContainer}>
              <Image
                source={getStepImage(3)}
                style={styles.guideImage}
                resizeMode="contain"
              />
            </View>
            <View style={styles.instructionContainer}>
              {GuideTexts.step3Instructions.title && (
                <Text style={styles.instructionTitle}>{GuideTexts.step3Instructions.title}</Text>
              )}
              <View style={styles.instructionList}>
                {(GuideTexts.step3Instructions.steps as string[]).map((step: string, index: number) => (
                  <View key={index} style={styles.instructionItemRow}>
                    <Text style={styles.instructionNumber}>{index + 1}.</Text>
                    <Text style={styles.instructionText}>{step}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* Bước 4: Nhận vé & mã QR */}
          <View style={styles.stepContainer}>
            <View style={styles.stepHeader}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>4</Text>
              </View>
              <Text style={styles.stepTitle}>{GuideTexts.step4Title}</Text>
            </View>
            <View style={styles.imageContainer}>
              <Image
                source={getStepImage(4)}
                style={styles.guideImage}
                resizeMode="contain"
              />
            </View>
            <View style={styles.instructionContainer}>
              {GuideTexts.step4Instructions.title && (
                <Text style={styles.instructionTitle}>{GuideTexts.step4Instructions.title}</Text>
              )}
              <View style={styles.instructionList}>
                {(GuideTexts.step4Instructions.steps as string[]).map((step: string, index: number) => (
                  <View key={index} style={styles.instructionItemRow}>
                    <Text style={styles.instructionNumber}>{index + 1}.</Text>
                    <Text style={styles.instructionText}>{step}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* Bước 5: In vé tại quầy (tùy chọn) */}
          <View style={styles.stepContainer}>
            <View style={styles.stepHeader}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>5</Text>
              </View>
              <Text style={styles.stepTitle}>{GuideTexts.step5Title}</Text>
            </View>
            <View style={styles.imageContainer}>
              <Image
                source={getStepImage(5)}
                style={styles.guideImage}
                resizeMode="contain"
              />
            </View>
            {/* step5Instructions không có title nên không render instructionTitle cho bước này */}
            <View style={styles.instructionContainer}>
              <View style={styles.instructionList}>
                {(GuideTexts.step5Instructions.steps as string[]).map((step: string, index: number) => (
                  <View key={index} style={styles.instructionItemRow}>
                    <Text style={styles.instructionNumber}>{index + 1}.</Text>
                    <Text style={styles.instructionText}>{step}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* Lưu ý quan trọng */}
          <View style={styles.noteContainer}>
            <View style={styles.noteHeader}>
              <MaterialIcons name="warning" size={28} color="#FF6B6B" />
              <Text style={styles.noteTitle}>{GuideTexts.importantNotes.title}</Text>
            </View>
            <View style={styles.noteList}>
              {(GuideTexts.importantNotes.notes as string[]).map((note: string, index: number) => (
                <Text key={index} style={styles.noteItem}>• {note}</Text>
              ))}
            </View>
          </View>

          {/* Thông tin liên hệ */}
          <View style={styles.contactContainer}>
            <Text style={styles.contactTitle}>{GuideTexts.contactInfo.title}</Text>
            <View style={styles.contactList}>
              {GuideTexts.contactInfo.contacts.map((contact, index) => (
                <View key={index} style={styles.contactItem}>
                  <MaterialIcons name={GuideIcons.contact[index] as any} size={26} color="#2D5BFF" />
                  <Text style={styles.contactText}>{contact.text}</Text>
                </View>
              ))}
            </View>
          </View>
          <Text style={styles.poweredBy}>{GuideTexts.poweredBy}</Text>
        </ScrollView>

      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 88, // hoặc 80
    paddingHorizontal: 16,
    justifyContent: 'flex-start', // hoặc 'center' nếu muốn căn giữa toàn bộ
  },
  backButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 12,
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  mainTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
    paddingHorizontal: 20,
  },
  stepContainer: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepNumber: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2D5BFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 36,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D5BFF',
    flex: 1,
    textAlignVertical: 'center',
    height: 36,
    lineHeight: 36,
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  guideImage: {
    width: '100%',
    height: 900,
    borderRadius: 12,
    backgroundColor: '#f5f5f5',
  },
  instructionContainer: {
    backgroundColor: '#F8FAFF',
    borderRadius: 12,
    padding: 16,
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D5BFF',
    marginBottom: 12,
  },
  instructionList: {
    gap: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  instructionItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  instructionNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5BFF',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(45,91,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  instructionText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    fontWeight: '500',
  },
  noteContainer: {
    backgroundColor: '#FFF8E1',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderLeftWidth: 6,
    borderLeftColor: '#FFA000',
    shadowColor: '#FFA000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
  },
  noteTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFA000',
    marginLeft: 10,
  },
  noteList: {
    gap: 8,
  },
  noteItem: {
    fontSize: 15,
    color: '#333',
    lineHeight: 22,
    marginBottom: 4,
  },
  contactContainer: {
    backgroundColor: 'rgba(45,91,255,0.1)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
  },
  contactList: {
    gap: 10,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 15,
    color: '#333',
    marginLeft: 10,
    fontWeight: '500',
    flex: 1,
  },
  contactTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2D5BFF',
    marginBottom: 12,
    marginLeft: 2,
  },
  poweredBy: {
    textAlign: 'center',
    color: '#2D5BFF',
    fontStyle: 'italic',
    fontWeight: 'bold',
    fontSize: 16,
    textShadowColor: 'rgba(255,255,255,0.7)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    marginTop: 32,
    marginBottom: 16,
  },
});

