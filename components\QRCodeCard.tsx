import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import QRCode from 'react-native-qrcode-svg';

interface QRCodeCardProps {
  qrCodeBase64?: string;
  ticketNumber: string;
  onPress: () => void;
  size?: number;
  tapToEnlargeText?: string;
}

const QRCodeCard: React.FC<QRCodeCardProps> = ({
  qrCodeBase64,
  ticketNumber,
  onPress,
  size = 140,
  tapToEnlargeText,
}) => (
  <TouchableOpacity style={styles.wrapper} activeOpacity={0.8} onPress={onPress}>
    {qrCodeBase64 ? (
      <Image
        source={{ uri: `data:image/png;base64,${qrCodeBase64}` }}
        style={[styles.qr, { width: size, height: size }]}
        resizeMode="contain"
      />
    ) : (
      <View style={[styles.qr, { width: size, height: size }]}> 
        <QRCode value={ticketNumber || 'DEFAULT-CODE'} size={size} />
      </View>
    )}
    <Text style={styles.ticket}>{ticketNumber}</Text>
    {tapToEnlargeText ? (
      <Text style={styles.tapToEnlarge}>{tapToEnlargeText}</Text>
    ) : null}
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  wrapper: {
    borderWidth: 1,
    borderColor: '#2D5BFF',
    borderRadius: 16,
    backgroundColor: '#fff',
    shadowColor: '#2D5BFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
    alignItems: 'center',
    margin: 12,
    width: 160,
    paddingVertical: 12,
  },
  qr: {
    marginBottom: 8,
  },
  ticket: {
    alignSelf: 'center',
    marginTop: 4,
    fontWeight: '500',
    fontSize: 15,
    color: '#222',
    letterSpacing: 1,
  },
  tapToEnlarge: {
    marginTop: 2,
    fontSize: 12,
    color: '#2D5BFF',
    textAlign: 'center',
    fontWeight: '400',
  },
});

export default QRCodeCard; 