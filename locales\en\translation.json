{"common": {"welcome": "Welcome to", "hello": "Hello, {{name}}!", "selectLanguage": "Select Language", "appName": "Ticket Booking System", "poweredBy": "Powered by G-TECH", "apply": "Apply", "confirm": "Confirm", "today": "Today"}, "welcomeScreen": {"welcomeMessage": "Book tickets quickly, conveniently and safely", "bookTicket": "Select Trip", "bookingGuide": "Booking Guide", "guide": "Guide", "mainTitle1": "We're", "mainTitle2": "going", "mainTitle3": "on a trip.", "subtitle": "Are you in?", "getStarted": "Get started", "backgroundText1": "FIND", "backgroundText2": "YOUR", "backgroundText3": "BUS"}, "bookingGuide": {"mainTitle": "Automatic Ticket Booking Guide", "subtitle": "Follow the steps below to book your bus ticket easily", "step1Title": "Select destination", "step1InstructionsTitle": "How to do:", "step1Instructions": ["Tap 'Find destination' in the center of the screen", "Start typing the city you’re traveling to", "Choose your destination from the list", "Tap 'CONFIRM' to proceed"], "step2Title": "Select trip", "step2InstructionsTitle": "How to do:", "step2Instructions": ["Select the departure date from the horizontal bar below", "View trip information: departure time, duration, bus type, ticket price", "Tap 'Schedule' to view detailed itinerary", "Tap 'Policy' to view cancellation policy", "Tap 'Book' to select ticket quantity and enter personal information"], "step3Title": "Enter booking information", "step3InstructionsTitle": "How to do:", "step3Instructions": ["Choose how many tickets you want: Tap '+' or '–' to adjust the number.", "Enter full name, phone number, email (if any)", "Agree to the terms: Tick the box to continue.", "Complete booking: Tap 'Book now' to finish booking."], "step4Title": "Review information and pay", "step4InstructionsTitle": "How to do:", "step4Instructions": ["Check your trip and personal information entered before", "If all information is correct, scan the QR code to pay", "Then the screen will switch to the print ticket screen and the machine will start printing the ticket"], "step5Title": "Ticket information screen", "step5Instructions": ["Please wait a few seconds for the system to print the paper ticket.", "Do not leave the machine until you receive your ticket.", "If the machine does not print, please contact staff or call Hotline: +84934657890.", "You can take a photo of the QR code below to use as a replacement for the paper ticket if needed."], "importantNotesTitle": "Important Notes", "importantNotes": ["Arrive at the bus station 30 minutes before departure time", "Bring valid identification documents", "Hand luggage up to 10kg/person", "No smoking or drinking alcohol on the bus", "You can cancel your ticket 24h in advance to get a 70% refund"], "contactTitle": "Need Support ?", "contactPhone": "Hotline: 1900-1234", "contactFacebook": "Fanpage: G-Tech Bus", "contactEmail": "Email: <EMAIL>", "poweredBy": "Powered by G-TECH"}, "tripSelection": {"title": "Select Trip", "dateTitle": "Select Departure Date", "departure": "Departure", "destination": "Destination", "departureDate": "Departure Date", "search": "Search", "back": "Back", "confirm": "Confirm", "selectDeparture": "Select Departure", "selectDestination": "Select Destination", "selectedDate": "Selected Date", "searchPlaceholder": "Search location..."}, "emptyState": {"selectToTitle": "Select destination", "selectToSubtitle": "To find a suitable trip for you", "notFoundTitle": "No trips found", "notFoundSubtitle": "Try changing the date or adjusting the filters", "fast": "Fast", "reliable": "Reliable", "promotion": "Promotion"}, "passengerInfo": {"title": "Booking Information", "ticketPrice": "Ticket Price", "ticketQuantity": "Ticket Quantity", "tickets": "tickets", "totalPayment": "Total Payment", "tripDetails": "Purchaser Information", "passengerName": "Passenger Name", "enterName": "Enter full name", "phoneNumber": "Phone Number", "enterPhone": "Enter phone number", "email": "Email for Information", "enterEmail": "Enter email address", "agreeToTerms": "I agree to the", "termsAndConditions": "terms and conditions", "bookNow": "Book Now", "pickupDropoff": "Pickup & Dropoff Points", "selectPickup": "Select Pickup Point", "selectDropoff": "Select Dropoff Point", "pickupPoint": "Pickup Point", "dropoffPoint": "Dropoff Point", "singleLocation": "Single Location", "multipleLocations": "Multiple Locations", "errorNameRequired": "Please enter your full name", "errorPhoneRequired": "Please enter your phone number", "errorPhoneInvalid": "Invalid phone number", "errorEmailRequired": "Please enter your email", "errorEmailInvalid": "Invalid email format", "errorNameTooShort": "Name must be at least 2 characters", "errorNameTooLong": "Name must be less than 50 characters", "errorEmailTooLong": "Email must be less than 100 characters"}, "tripScreen": {"schedule": "Schedule", "policy": "Policy", "cancellationPolicy": "Cancellation Policy", "luggagePolicy": "Luggage Policy", "otherPolicies": "Other Policies", "bookNow": "Book Now", "filter": "Filter", "sort": "Sort", "priceDesc": "Price High to Low", "priceAsc": "Price Low to High", "departureEarliest": "Earliest Departure", "departureLatest": "Latest Departure", "selectFrom": "Select Departure", "selectTo": "Destination", "pleaseSelectLocation": "Please select departure and destination locations", "loading": "Loading trips...", "emptySeats": "Empty seats", "priceLabel": "Price", "busName": "Bus name", "busType": "Bus type", "availableSeats": "Available seats", "contactForPrice": "Contact", "unknown": "---"}, "filterModal": {"title": "Search Filter", "sort": "Sort", "departureTime": "Departure Time", "busType": "Bus Type", "seat": "<PERSON><PERSON>", "bed": "Bed", "limousine": "Limousine", "earlyMorning": "Early Morning 00:00 - 06:00", "morning": "Morning 06:00 - 12:00", "afternoon": "Afternoon 12:00 - 18:00", "evening": "Evening 18:00 - 24:00", "priceDesc": "Price high to low", "priceAsc": "Price low to high", "departureEarliest": "Earliest departure", "departureLatest": "Latest departure", "reset": "Reset", "defaultSort": "Default (no sort)"}, "placeModal": {"title": "Select Arrival", "from": "From", "to": "Arrival", "searchFrom": "Search departure...", "searchTo": "Search arrival...", "confirm": "CONFIRM"}, "ticketConfirmation": {"title": "Ticket Information", "reviewInfo": "Review Ticket Information", "pleaseReview": "Please review your information before payment", "tripInfo": "Trip Information", "busCompany": "Bus Company", "route": "Route", "date": "Date", "time": "Departure Time", "duration": "Duration", "arrivalTime": "Arrival Time", "busType": "Bus Type", "locationInfo": "Pickup & Dropoff Points", "pickup": "Pickup Point", "dropoff": "Dropoff Point", "paymentInfo": "Payment Information", "ticketQuantity": "Ticket Quantity", "ticketPrice": "Ticket Price", "totalPrice": "Total Price", "passengerInfo": "Passenger Information", "fullName": "Full Name", "phoneNumber": "Phone Number", "email": "Email", "licensePlate": "License Plate", "proceedToPayment": "Proceed to Payment", "quantity": "Quantity", "totalAmount": "Total Amount"}, "payment": {"title": "Online Payment", "qrExpires": "QR code expires in", "tapToEnlarge": "Tap to enlarge", "refreshCode": "Refresh code", "guide": "Guide", "transferInfo": "Transfer Information", "bank": "Bank", "accountNumber": "Account Number", "accountHolder": "Account Holder", "totalAmount": "Total Amount", "content": "Content", "optional": "Optional", "totalPayment": "Total Payment", "iHavePaid": "I have transferred", "scanQrToPay": "Scan QR code to pay", "scanInstructions": "Place QR code in the scanning area of your banking app", "timeLeft": "QR code expires in", "scanQRCode": "Scan QR Code", "copyCode": "Copy Code", "share": "Share", "paymentDetails": "Payment Details", "orderCode": "Order Code", "route": "Route", "departureTime": "Departure Time", "quantity": "Quantity", "amount": "Amount", "confirmPayment": "Confirm Payment"}, "printTicket": {"title": "Print Ticket", "busType": "Bus Type", "passengerInfo": {"title": "Passenger Information", "fullName": "Full Name", "phoneNumber": "Phone Number", "email": "Email"}, "tapToEnlarge": "Tap to enlarge", "downloadTicket": "Download Ticket", "qrCode": "QR Code", "scanQRCode": "Scan this QR code to verify ticket", "fleetName": "Bus Company", "licensePlate": "License Plate", "departureTime": "Departure Time", "arrivalTime": "Arrival Time", "price": "Ticket Price", "hotline": "Hotline", "notes": "Notes"}, "vehicleType": {"bed": "Sleeper bus", "chair": "<PERSON><PERSON>", "limousine": "Limousine"}}