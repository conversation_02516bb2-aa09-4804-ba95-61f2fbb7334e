import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import React from 'react';
import '../i18n';
import './globals.css';

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    'Roboto-Regular': require('../assets/fonts/Roboto/static/Roboto-Regular.ttf'),
    'Roboto-Bold': require('../assets/fonts/Roboto/static/Roboto-Bold.ttf'),
    'Roboto-Italic': require('../assets/fonts/Roboto/static/Roboto-Italic.ttf'),
    'Roboto-Medium': require('../assets/fonts/Roboto/static/Roboto-Medium.ttf'),
  });
  React.useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    } else {
      SplashScreen.preventAutoHideAsync();
    }
  }, [fontsLoaded]);
  if (!fontsLoaded) {
    return null;
  }
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="trips" />
      <Stack.Screen name="ticket" />
      <Stack.Screen name="vehicles" />
      <Stack.Screen name="passenger-info" />
      <Stack.Screen name="booking-guide/index" />
      <Stack.Screen name="confirmation" />
    </Stack>
  );
}
