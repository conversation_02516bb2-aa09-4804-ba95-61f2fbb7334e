import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, StyleSheet, View } from 'react-native';
import { IconButton, Surface, Text } from 'react-native-paper';

interface TripInfoModalProps {
  visible: boolean;
  onClose: () => void;
  type: 'schedule' | 'policy' | 'terms';
  tripId?: string;
}

const TripInfoModal = ({ visible, onClose, type, tripId }: TripInfoModalProps) => {
  const { t, i18n } = useTranslation();

  // Mock data for schedule and policy based on current language
  const scheduleData = i18n.language === 'en' ? [
    { time: '06:00', description: 'Departure from Da Nang Bus Station' },
    { time: '08:30', description: 'Rest stop at Quang Ngai Station (15 minutes)' },
    { time: '12:00', description: 'Lunch break at Quy Nhon (45 minutes)' },
    { time: '16:30', description: 'Rest stop at Nha Trang Station (15 minutes)' },
    { time: '21:00', description: 'Arrival at Mien Dong Bus Station, Ho Chi Minh City' },
  ] : [
    { time: '06:00', description: 'Khởi hành từ bến xe Đà Nẵng' },
    { time: '08:30', description: 'Dừng nghỉ tại trạm dừng Quảng Ngãi (15 phút)' },
    { time: '12:00', description: 'Dừng nghỉ ăn trưa tại Quy Nhơn (45 phút)' },
    { time: '16:30', description: 'Dừng nghỉ tại trạm dừng Nha Trang (15 phút)' },
    { time: '21:00', description: 'Đến bến xe Miền Đông, TP. Hồ Chí Minh' },
  ];

  const policyData = {
    cancellation: i18n.language === 'en' ? [
      'Cancel 24h before: 70% refund',
      'Cancel 12h before: 50% refund',
      'Cancel within 12h: No refund'
    ] : [
      'Hủy trước 24h: Hoàn 70% giá vé',
      'Hủy trước 12h: Hoàn 50% giá vé',
      'Hủy trong vòng 12h: Không hoàn tiền'
    ],
    luggage: i18n.language === 'en' ? [
      'Carry-on luggage: Maximum 10kg/person',
      'Check-in luggage: Maximum 20kg/person',
      'No transportation of prohibited or flammable items'
    ] : [
      'Hành lý xách tay: Tối đa 10kg/người',
      'Hành lý ký gửi: Tối đa 20kg/người',
      'Không vận chuyển hàng hóa cấm, dễ cháy nổ'
    ],
    other: i18n.language === 'en' ? [
      'Arrive at the bus station 30 minutes before departure',
      'Bring valid identification documents',
      'No smoking or drinking alcohol on the bus'
    ] : [
      'Có mặt tại bến xe trước 30 phút',
      'Mang theo giấy tờ tùy thân hợp lệ',
      'Không hút thuốc, uống rượu bia trên xe'
    ]
  };

  const renderScheduleContent = () => (
    <View style={styles.scheduleContainer}>
      {scheduleData.map((item, index) => (
        <View key={index} style={styles.scheduleItem}>
          <View style={styles.timelineContainer}>
            <View style={styles.timelineDot} />
            {index < scheduleData.length - 1 && <View style={styles.timelineLine} />}
          </View>
          <View style={styles.scheduleContent}>
            <Text style={styles.scheduleTime}>{item.time}</Text>
            <Text style={styles.scheduleDescription}>{item.description}</Text>
          </View>
        </View>
      ))}
    </View>
  );

  const renderPolicyContent = () => (
    <View style={styles.policyContainer}>
      <View style={styles.policySection}>
        <Text style={styles.policySectionTitle}>{t('tripScreen.cancellationPolicy')}</Text>
        {policyData.cancellation.map((item, index) => (
          <View key={index} style={styles.policyItem}>
            <MaterialIcons name="check-circle" size={18} color="#2D5BFF" style={styles.policyIcon} />
            <Text style={styles.policyText}>{item}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.policySection}>
        <Text style={styles.policySectionTitle}>{t('tripScreen.luggagePolicy')}</Text>
        {policyData.luggage.map((item, index) => (
          <View key={index} style={styles.policyItem}>
            <MaterialIcons name="check-circle" size={18} color="#2D5BFF" style={styles.policyIcon} />
            <Text style={styles.policyText}>{item}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.policySection}>
        <Text style={styles.policySectionTitle}>{t('tripScreen.otherPolicies')}</Text>
        {policyData.other.map((item, index) => (
          <View key={index} style={styles.policyItem}>
            <MaterialIcons name="check-circle" size={18} color="#2D5BFF" style={styles.policyIcon} />
            <Text style={styles.policyText}>{item}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const termsSections = [
    {
      title: 'I. CHÍNH SÁCH GIÁ VÉ',
      items: [
        '1. We áp dụng 100% giá vé đối với người lớn và trẻ em ngồi ghế riêng.',
        '2. Nếu bạn đặt trên 20 vé, We tặng bạn mã giảm giá trên tổng giá trị đơn hàng, áp dụng cho tất cả các hạng vé. Bạn vui lòng liên hệ We để được nhận ưu đãi trước khi thực hiện đặt vé.',
        '3. Toàn bộ chương trình ưu đãi của We chỉ được áp dụng 01 lần đối với 01 đơn hàng và không áp dụng đồng thời với các chương trình ưu đãi khác. Các mã ưu đãi chỉ áp dụng khi đặt vé trực tiếp với bạn tổ chức của We.',
        '4. Giá trị đơn hàng không bao gồm đồ ăn, thức uống và các dịch vụ khác (trừ trường hợp được bạn tổ chức đính kèm thông tin).',
      ]
    },
    {
      title: 'II. CHÍNH SÁCH GIÁ VÉ ĐỐI VỚI TRẺ EM:',
      items: [
        '1. Với các bé dưới 3 tuổi, We xin phép KHÔNG NHẬN tham gia chương trình vì không gian có âm thanh lớn và đông người, dễ ảnh hưởng đến tâm lý và sức khỏe trẻ nhỏ.',
        '2. Với bé trên 3 tuổi cần có ghế ngồi riêng, We tính phí vé TƯƠNG ĐƯƠNG như người lớn. Mỗi vé tương ứng với 01 ghế ngồi trong 01 show diễn.',
        '3. Riêng với hạng vé khu vực NGỒI THUYỀN: We KHUYẾN CÁO các bé dưới 1m2 nên ngồi khu vực NGỒI THỀM thay vì NGỒI THUYỀN để đảm bảo an toàn tuyệt đối trong quá trình show diễn ra.',
      ]
    },
    {
      title: 'III. ĐẶT CHỖ & THANH TOÁN',
      items: [
        '1. Hình thức đặt vé trực tuyến (online) được áp dụng cho tất cả các chương trình của We.',
        '2. Vé tham dự We SHOW chỉ có thể được mua trực tuyến thông qua website https://Weshow.vn hoặc các điểm bán vé trực tiếp được ban tổ chức công bố trên Fanpage. We không chịu trách nhiệm pháp lý đối với bất kỳ việc mua vé nào qua các kênh trái phép, mua bán – trao đổi lại từ bên thứ ba.',
        '3. Mỗi đơn hàng chỉ được đặt tối đa 05 vé.',
        '4. Bạn cần hoàn thành đặt chỗ trong vòng 05 phút tính từ thời điểm đặt chỗ. Các đặt chỗ không thực hiện đúng, đủ các bước và đảm bảo thời gian theo quy định sẽ tự động bị hủy mà không cần thông báo trước.',
        '5. Bạn có trách nhiệm kiểm tra kỹ thông tin đặt vé của mình trước khi thực hiện thanh toán. Mọi thông tin sẽ không thể sửa đổi sau khi thao tác đặt vé hoàn tất. Khi nhận vé điện tử qua email từ ban tổ chức, bạn vui lòng kiểm tra kỹ thông tin trên email và liên hệ ngay với ban tổ chức nếu có sai sót.',
        '6. Trong trường hợp bạn gặp lỗi phát sinh không thể thực hiện thao tác đặt vé trên website của We, vui lòng liên hệ ban tổ chức để được hỗ trợ xử lý kịp thời.',
        '7. Sau khi bạn đã hoàn thành thanh toán trực tuyến, trong vòng 06 tiếng, We sẽ liên hệ bạn qua số điện thoại đã đăng ký để xác nhận thông tin đặt vé và gửi vé điện tử qua địa chỉ thư (email) đã được cung cấp. Nếu bạn đặt vé & thanh toán sau 22h, BTC sẽ liên hệ xác nhận đặt vé sau 8h sáng ngày hôm sau.',
        '8. Sau khi hoàn thành đặt chỗ & thanh toán, nếu bạn chưa nhận bất kỳ xác nhận (email/ điện thoại) nào từ We trong thời gian trên thì vui lòng liên hệ hotline CSKH hoặc Fanpage để được hỗ trợ. Nếu bạn không liên hệ lại, đồng nghĩa việc thanh toán đã được xác nhận, BTC đã hoàn thành nghĩa vụ đặt chỗ và xác nhận thông tin cho bạn.',
        '9. Bạn có trách nhiệm tự bảo quản nguyên vẹn (đối với vé cứng) và bảo mật thông tin mã vé (đối với vé điện tử) trước khi check-in tham gia chương trình. Vé có thể bị thu hồi nếu bạn làm mất hoặc để lộ thông tin mã vé cho người khác.',
        '10. Vui lòng xếp hàng khi thực hiện check-in; không chen lấn xô đẩy trước, trong và sau khi sự kiện diễn ra.',
        '11. Không gây gổ, đánh nhau hoặc tham gia vào bất kì hành động nào có thể gây hại, gây nguy hiểm, đe dọa hoặc mang lại sự khó chịu cho bất cứ ai, bao gồm nhưng không giới hạn khán giả và nhân viên của ban tổ chức.',
        '12. Không gây thiệt hại, phá huỷ hoặc trộm cắp bất kì tài sản nào tại địa điểm diễn ra chương trình.',
        '13. Không xâm phạm các khu vực hạn chế mà không có sự cho phép của ban tổ chức.',
        '14. Tham gia We đồng nghĩa rằng bạn đồng ý với việc hình ảnh của mình được khai thác bất kỳ lúc nào trong bất kỳ sản phẩm phim, ảnh, âm thanh và/hoặc bạn ghi hình nghe nhìn nào, trên tất cả các phương tiện truyền thông cho mục đích quảng bá của We.',
      ]
    },
    {
      title: 'IV. VẬT DỤNG KHÔNG ĐƯỢC MANG VÀO CHƯƠNG TRÌNH',
      items: [
        '1. Các loại vật thể bay, máy bay không người lái, máy quay điều khiển từ xa…;',
        '2. Các thiết bị thu âm, quay, chụp chuyên nghiệp/ bán chuyên nghiệp và máy quay cầm tay, các loại ống kính;',
        '3. Các loại cán cờ, gậy selfie, monopod, tripod, gimbal,…;',
        '4. Vũ khí và vật sắc nhọn;',
        '5. Ô dù các loại;',
        '6. Vali quá khổ;',
        '7. Các loại cờ, banner cổ vũ vượt quá kích thước 30x10cm;',
        '8. Poster/ banner/ quốc huy/ bảng chỉ dẫn hay bất cứ vật dụng liên quan mang tính chất chính trị, chống phá nhà nước CHXHCN Việt Nam và có ngôn ngữ không phù hợp thuần phong mỹ tục;',
        '9. Thú cưng (trừ chó dẫn đường);',
        '10. Ma túy, thuốc lá, thuốc lá điện tử, chất kích thích, đồ uống có cồn và các loại chất cấm khác;',
        '11. Bật lửa, diêm, pháo hoa, pháo sáng và các vật dụng dễ gây cháy nổ khác;',
        '12. Loa, còi, kèn, mic hát karaoke di động hoặc các vật dụng có thể gây tiếng ồn khác;',
        '13. Ghế ngồi (bao gồm các loại ghế ngồi nhỏ, ghế ngồi di động);',
        '14. Đèn pin; con trỏ laser, các loại đèn laser;',
        '15. Các chai/lọ dạng xịt (ngoại trừ dung dịch sát khuẩn tay dung tích dưới 50ml, các loại thuốc được chỉ định bởi bác sĩ);',
        '16. Chai thủy tinh, chai nhựa, chai nhôm, lọ thiếc hay các vật chứa bằng nhựa cứng;',
        '17. Đồ ăn, thức uống từ bên ngoài chương trình;',
        '18. Các loại tài liệu tài trợ, quảng cáo hoặc tiếp thị của các đơn vị/ doanh nghiệp không hợp tác với We;',
        '19. Tất cả các vật phẩm khác được bạn tổ chức cho là không phù hợp với chương trình;',
      ]
    },
    {
      title: 'V. CHÍNH SÁCH VI PHẠM',
      items: [
        '1. We sẽ không chịu trách nhiệm pháp lý về vấn đề mất mát tài sản, thương tích hoặc thiệt hại xảy ra do bạn vi phạm bất kỳ quy định nào trong sự kiện.',
        '2. Vé tham dự là tài sản của We. Bất kỳ vé nào vi phạm Điều khoản & Chính sách của We đều bị BTC vô hiệu. Nếu bạn sử dụng vé vô hiệu sẽ bị từ chối hoặc mời ra khỏi chương trình mà không được hoàn bất kỳ khoản phí nào và có thể bị kiện.',
        '3. We có quyền từ chối sự tham gia của bất kỳ khán giả nào vi phạm các Điều khoản & Chính sách, không tuân thủ quy định của chương trình và không hoàn trả lại vé.',
        '4. Trong mọi trường hợp, quyết định của BTC là quyết định cuối cùng.',
      ]
    },
    {
      title: 'MỌI THÔNG TIN, THẮC MẮC CẦN HỖ TRỢ GIẢI ĐÁP, BẠN VUI LÒNG LIÊN HỆ We QUA:',
      items: [
        '1. Hotline: 123456789',
        '2. Fanpage: gtech',
        '3. Email: ',
      ]
    }
  ];

  const renderTermsContent = () => (
    <View>
      <Text style={styles.termsIntro}>
        Tất cả các vé bán tuân theo các Điều khoản & Chính sách của Ban Tổ Chức. Khi sử dụng dịch vụ thanh toán và đặt vé tham gia We, đồng nghĩa rằng bạn hoàn toàn chấp thuận tất cả các Điều khoản & Chính sách của We. BTC không chịu trách nhiệm khi bạn không thực hiện đúng các quy định trong Điều khoản này. Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ với We để được hỗ trợ. We cảm ơn bạn!
      </Text>
      {termsSections.map((section, idx) => (
        <View key={idx} style={{marginTop: 16}}>
          <Text style={styles.termsSectionTitle}>{section.title}</Text>
          {section.items.map((item, i) => (
            <Text key={i} style={styles.termsItem}>{item}</Text>
          ))}
        </View>
      ))}
    </View>
  );

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <Surface style={styles.modalView} elevation={5}>
          <LinearGradient
            colors={['#2D5BFF', '#4A90E2']}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.modalTitle}>
              {type === 'schedule' ? t('tripScreen.schedule') : type === 'policy' ? t('tripScreen.policy') : 'Điều khoản & Chính sách vé'}
            </Text>
            <IconButton
              icon="close"
              iconColor="#FFFFFF"
              size={24}
              onPress={onClose}
              style={styles.closeButton}
            />
          </LinearGradient>
          <ScrollView 
            style={styles.contentContainer}
            contentContainerStyle={{ paddingBottom: 48 }}
          >
            {type === 'schedule' && renderScheduleContent()}
            {type === 'policy' && renderPolicyContent()} 
            {type === 'terms' && renderTermsContent()}
          </ScrollView>
        </Surface>
      </View>
    </Modal>
  );
};


const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  modalView: {
    width: '85%',
    maxWidth: 600,
    maxHeight: '80%',
    backgroundColor: '#F8FAFF',
    borderRadius: 24,
    overflow: 'hidden',
  },
  headerGradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    margin: 0,
  },
  contentContainer: {
    padding: 20,
  },

  container: {
    padding: 20,
  },
  // title: {
  //   fontWeight: 'bold',
  //   fontSize: 18,
  //   marginTop: 16,
  //   marginBottom:4,
  // },
  // item: {
  //   fontSize: 16,
  //   marginLeft: 12,
  //   marginTop: 4,
  //   lineHeight: 16,
  // },
  // Schedule styles
  scheduleContainer: {
    paddingVertical: 20,
  },
  scheduleItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineContainer: {
    width: 24,
    alignItems: 'center',
  },
  timelineDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#2D5BFF',
  },
  timelineLine: {
    width: 2,
    height: '100%',
    backgroundColor: '#2D5BFF',
    position: 'absolute',
    top: 16,
    left: 11,
  },
  scheduleContent: {
    flex: 1,
    marginLeft: 12,
  },
  scheduleTime: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  scheduleDescription: {
    fontSize: 14,
    color: '#666',
  },
  // Policy styles
  policyContainer: {
    paddingVertical: 10,
  },
  policySection: {
    marginBottom: 20,
  },
  policySectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  policyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  policyIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  policyText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    paddingTop: 3,
  },
  termsIntro: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
    fontWeight: 'bold',
  },
  termsSectionTitle: {
    fontWeight: 'bold',
    fontSize: 18,
    color: '#222',
    marginBottom: 6,
  },
  termsItem: {
    fontSize: 16,
    color: '#444',
    marginLeft: 30,
    marginBottom: 2,
    lineHeight: 20,
  },
});

export default TripInfoModal;
