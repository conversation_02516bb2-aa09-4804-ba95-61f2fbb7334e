import axiosInstance from "./axiosInstance";

export const createOrder = (orderData: any) =>
  axiosInstance.post("/api/v1/orders", orderData);

export const confirmPayment = async (orderId: string) => {
  try {
    const response = await axiosInstance.put(`/api/v1/orders/${orderId}/status`, {
      payment_status: 'paid',
      note: 'payment confirmed via test endpoint',
      issue_ticket: true,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}; 