# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Base project structure

/app                      # Expo Router (file-based routing)
  /_layout.tsx            # Root layout component
  /index.tsx              # Welcome screen
  /trips/                 # Chọn chuyến đi
    /index.tsx            # Danh sách chuyến đi
    /[tripId].tsx         # Chi tiết chuyến đi
  /vehicles/              # Chọn xe
    /index.tsx            # Danh sách xe cho chuyến đã chọn
    /[vehicleId].tsx      # Chi tiết xe
  /passenger-info.tsx     # Nhập thông tin hành khách
  /payment.tsx            # Thanh toán
  /confirmation.tsx       # Xác nhận đặt vé thành công

/components               # Reusable components
  /ui/                    # UI components
    /Button.tsx           # Button component
    /Card.tsx             # Card component
    /Input.tsx            # Input component
    /Select.tsx           # Select component
    /StepIndicator.tsx    # Hiển thị các bước đặt vé
  /trip/                  # Trip related components
    /TripCard.tsx         # Card hiển thị thông tin chuyến
    /TripFilter.tsx       # Bộ lọc chuyến đi
  /vehicle/               # Vehicle related components
    /VehicleCard.tsx      # Card hiển thị thông tin xe
    /SeatSelector.tsx     # Component chọn ghế
  /payment/               # Payment related components
    /PaymentMethod.tsx    # Component chọn phương thức thanh toán
    /PriceBreakdown.tsx   # Component hiển thị chi tiết giá

/constants                # App constants
  /Colors.ts              # Color definitions
  /Layout.ts              # Layout constants
  /Steps.ts               # Các bước trong quy trình đặt vé

/hooks                    # Custom React hooks
  /useBooking.ts          # Hook quản lý thông tin đặt vé
  /useTrips.ts            # Hook lấy dữ liệu chuyến đi
  /useVehicles.ts         # Hook lấy dữ liệu xe

/services                 # API and other services
  /api.ts                 # API client setup
  /tripService.ts         # Service xử lý chuyến đi
  /bookingService.ts      # Service xử lý đặt vé
  /paymentService.ts      # Service xử lý thanh toán

/store                    # State management
  /bookingStore.ts        # Store lưu trữ thông tin đặt vé
  /uiStore.ts             # Store quản lý UI state

/types                    # TypeScript type definitions
  /trip.ts                # Types cho chuyến đi
  /vehicle.ts             # Types cho xe
  /passenger.ts           # Types cho thông tin hành khách
  /payment.ts             # Types cho thanh toán

/utils                    # Utility functions
  /date.ts                # Xử lý ngày tháng
  /format.ts              # Format dữ liệu
  /validation.ts          # Validate form

/assets                   # Static assets
  /images/                # Images
  /icons/                 # Icons

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.

## Hướng dẫn đặt vé xe

### Tổng quan về ứng dụng
Ứng dụng đặt vé xe bus được xây dựng với React Native và Expo, hỗ trợ đặt vé trực tuyến với giao diện thân thiện và quy trình đơn giản.

### Quy trình đặt vé

#### 1. Màn hình chào mừng (Welcome Screen)
- **Chọn ngôn ngữ**: Ứng dụng hỗ trợ tiếng Việt và tiếng Anh
- **Đặt vé ngay**: Nhấn nút "Đặt vé" để bắt đầu quy trình
- **Hướng dẫn**: Xem hướng dẫn chi tiết về cách sử dụng ứng dụng

#### 2. Chọn chuyến đi (Trip Selection)
- **Nhập thông tin tìm kiếm**:
  - Điểm đi (From): Chọn bến xe hoặc địa điểm xuất phát
  - Điểm đến (To): Chọn bến xe hoặc địa điểm đích
  - Ngày đi: Chọn ngày muốn đi
- **Xem danh sách chuyến**: Hiển thị các chuyến xe phù hợp với tiêu chí tìm kiếm
- **Lọc và sắp xếp**: 
  - Lọc theo thời gian khởi hành, giá vé, loại xe
  - Sắp xếp theo giá (tăng/giảm), thời gian khởi hành
- **Thông tin chuyến xe**:
  - Giờ khởi hành và đến
  - Thời gian di chuyển
  - Giá vé
  - Loại xe (ghế ngồi/giường nằm)
  - Tiện ích (WiFi, nước uống, snack)

#### 3. Nhập thông tin hành khách (Passenger Information)
- **Số lượng vé**: Chọn số lượng vé cần đặt (1-10 vé)
- **Thông tin cá nhân**:
  - Họ và tên (bắt buộc)
  - Số điện thoại (bắt buộc)
  - Email (bắt buộc)
- **Điểm đón/trả**:
  - Chọn điểm đón khách
  - Chọn điểm trả khách
  - Xem thông tin chi tiết địa chỉ và thời gian
- **Đồng ý điều khoản**: Tích vào ô đồng ý với điều khoản sử dụng

#### 4. Thanh toán (Payment)
- **Phương thức thanh toán**: 
  - Thanh toán qua QR Code (VTPay)
  - Chuyển khoản ngân hàng
- **Thông tin chuyển khoản**:
  - Ngân hàng: VIETINBANK
  - Số tài khoản: *********
  - Chủ tài khoản: CHUTAIKHOANDEMO
- **Mã QR**: 
  - Quét mã QR để thanh toán nhanh
  - Mã QR có thời hạn 5 phút
  - Có thể tải lại mã nếu cần
- **Hướng dẫn thanh toán**: Xem chi tiết cách thanh toán

#### 5. Xác nhận vé (Ticket Confirmation)
- **Thông tin chuyến đi**:
  - Mã vé: BK + số ngẫu nhiên
  - Nhà xe và tuyến đường
  - Ngày giờ khởi hành
  - Thời gian di chuyển
- **Thông tin hành khách**: Tên, số điện thoại, email
- **Thông tin thanh toán**: Phương thức và trạng thái thanh toán
- **Điểm đón/trả**: Địa chỉ và thời gian cụ thể
- **Tổng tiền**: Số lượng vé × giá vé

### Tính năng chính

#### 🔍 Tìm kiếm và lọc
- Tìm kiếm chuyến xe theo điểm đi/đến và ngày
- Lọc theo thời gian khởi hành, giá vé, loại xe
- Sắp xếp kết quả theo nhiều tiêu chí

#### 📱 Giao diện thân thiện
- Thiết kế Material Design với gradient đẹp mắt
- Responsive design cho mọi kích thước màn hình
- Animation mượt mà và trải nghiệm người dùng tốt

#### 🌐 Đa ngôn ngữ
- Hỗ trợ tiếng Việt và tiếng Anh
- Chuyển đổi ngôn ngữ dễ dàng
- Nội dung được dịch đầy đủ

#### 💳 Thanh toán an toàn
- Tích hợp thanh toán QR Code
- Hỗ trợ chuyển khoản ngân hàng
- Bảo mật thông tin thanh toán

#### 📧 Thông báo và xác nhận
- Hiển thị thông tin vé chi tiết
- Mã vé duy nhất để tra cứu
- Lưu trữ lịch sử đặt vé

### Cấu trúc dữ liệu

#### Trip (Chuyến xe)
```typescript
interface Trip {
  id: string;
  departureTime: string;
  departureStation: string;
  arrivalTime: string;
  arrivalStation: string;
  duration: string;
  price: number;
  vehicleType: string;
  features: string[];
  vehicleImage: any;
}
```

#### PassengerInfo (Thông tin hành khách)
```typescript
interface PassengerInfo {
  fullName: string;
  phoneNumber: string;
  email: string;
  agreeToTerms: boolean;
}
```

#### TicketData (Dữ liệu vé)
```typescript
interface TicketData {
  ticketId: string;
  busCompany: string;
  route: string;
  date: string;
  time: string;
  duration: string;
  arrivalTime: string;
  price: number;
  quantity: number;
  totalPrice: number;
  busType: string;
  pickupLocation: LocationInfo;
  dropoffLocation: LocationInfo;
  passengerInfo: PassengerInfo;
  paymentMethod: string;
  paymentStatus: string;
  bookingDate: string;
}
```

### Lưu ý quan trọng

1. **Thông tin bắt buộc**: Họ tên, số điện thoại và email là thông tin bắt buộc
2. **Thời gian thanh toán**: Mã QR có thời hạn 5 phút, cần thanh toán trong thời gian này
3. **Số lượng vé**: Tối đa 10 vé cho mỗi lần đặt
4. **Điểm đón/trả**: Chọn điểm đón/trả phù hợp với lịch trình
5. **Xác nhận thông tin**: Kiểm tra kỹ thông tin trước khi thanh toán

### Hỗ trợ kỹ thuật

Nếu gặp vấn đề trong quá trình đặt vé, vui lòng liên hệ:
- **Hotline**: 1900-xxxx
- **Email**: <EMAIL>
- **Chat online**: Có sẵn 24/7

---

## Đa ngôn ngữ (i18next)
- Đã cấu hình sẵn i18next với hai ngôn ngữ: tiếng Anh và tiếng Việt.
- Thay đổi ngôn ngữ bằng các nút trên trang chính.

## Tailwind CSS
- Đã cấu hình tailwind, có thể sử dụng class tailwind trong các component.
- Đảm bảo import file CSS chính (app/globals.css) vào entrypoint nếu là web.
